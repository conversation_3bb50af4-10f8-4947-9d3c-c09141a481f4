<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'subscription_plan',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    /**
     * Check if user is admin
     */
    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }

    /**
     * Check if user is regular user
     */
    public function isUser(): bool
    {
        return $this->role === 'user';
    }

    /**
     * Get the user's carousels
     */
    public function carousels(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(Carousel::class);
    }

    /**
     * Get the user's usage tracking records
     */
    public function usageTrackings(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(UsageTracking::class);
    }

    /**
     * Get the user's activity logs
     */
    public function activityLogs(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(ActivityLog::class);
    }

    /**
     * Get the user's template groups
     */
    public function templateGroups(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(TemplateGroup::class)->ordered();
    }

    /**
     * Get the user's templates
     */
    public function templates(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(Template::class)->ordered();
    }

    /**
     * Get the user's generated content
     */
    public function generatedContent(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(GeneratedContent::class)->recent();
    }
}
