@tailwind base;
@tailwind components;
@tailwind utilities;

/* CarouselGen UI Layout System */
:root {
  /* Light Theme Colors */
  --bg-primary: #f9fafb;        /* bg-gray-50 */
  --bg-secondary: #ffffff;      /* bg-white */
  --bg-tertiary: #f3f4f6;       /* bg-gray-100 */
  --text-primary: #111827;      /* text-gray-900 */
  --text-secondary: #6b7280;    /* text-gray-500 */
  --text-tertiary: #9ca3af;     /* text-gray-400 */

  /* Dark Theme Colors */
  --bg-primary-dark: #111827;   /* bg-gray-900 */
  --bg-secondary-dark: #1f2937; /* bg-gray-800 */
  --bg-tertiary-dark: #374151;  /* bg-gray-700 */
  --text-primary-dark: #f9fafb; /* text-gray-50 */
  --text-secondary-dark: #d1d5db; /* text-gray-300 */
  --text-tertiary-dark: #9ca3af; /* text-gray-400 */

  /* Accent Colors (consistent across themes) */
  --accent-primary: #4f46e5;    /* indigo-600 */
  --accent-hover: #4338ca;      /* indigo-700 */
  --accent-light: #eef2ff;      /* indigo-50 */
}

/* Base Layout Classes */
@layer components {
  .app-background {
    @apply bg-gray-50 dark:bg-gray-900 min-h-screen transition-colors duration-200;
  }

  .card-primary {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 transition-colors duration-200;
  }

  .card-secondary {
    @apply bg-gray-100 dark:bg-gray-700 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 transition-colors duration-200;
  }

  .card-elevated {
    @apply bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 transition-all duration-200 hover:shadow-xl;
  }

  .text-primary {
    @apply text-gray-900 dark:text-gray-100 transition-colors duration-200;
  }

  .text-secondary {
    @apply text-gray-600 dark:text-gray-300 transition-colors duration-200;
  }

  .text-tertiary {
    @apply text-gray-500 dark:text-gray-400 transition-colors duration-200;
  }

  .btn-primary {
    @apply bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-lg shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800;
  }

  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 font-medium py-2 px-4 rounded-lg shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-offset-gray-800;
  }

  .btn-sm {
    @apply text-xs py-1 px-2 rounded;
  }

  .input-field {
    @apply block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:focus:ring-indigo-400 dark:focus:border-indigo-400 transition-colors duration-200;
  }

  .auth-container {
    @apply min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-rose-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 transition-colors duration-200;
  }

  .auth-card {
    @apply max-w-md w-full space-y-8 bg-white dark:bg-gray-800 p-8 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 transition-colors duration-200;
  }
}
