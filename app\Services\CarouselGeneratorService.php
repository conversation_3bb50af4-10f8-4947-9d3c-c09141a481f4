<?php

namespace App\Services;

use App\Models\GeneratedContent;
use App\Models\Template;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;
use Intervention\Image\Typography\FontFactory;

class CarouselGeneratorService
{
    private ?ImageManager $imageManager;

    public function __construct()
    {
        // Try to use GD driver, fallback to Imagick if available
        try {
            $this->imageManager = new ImageManager(new Driver());
        } catch (\Exception $e) {
            // For now, we'll create a mock image manager for development
            // In production, you should install the GD extension
            $this->imageManager = null;
        }
    }

    /**
     * Process JSON data and generate carousel content.
     */
    public function generateCarousel(GeneratedContent $generatedContent): void
    {
        try {
            $generatedContent->update([
                'status' => 'processing',
                'started_at' => now(),
            ]);

            $template = $generatedContent->template;
            $sourceData = $generatedContent->source_data;
            $mappedData = $generatedContent->mapped_data;
            $config = $generatedContent->generation_config;

            // Validate template file exists
            if (!Storage::disk('public')->exists($template->file_path)) {
                throw new \Exception('Template file not found: ' . $template->file_path);
            }

            $generatedFiles = [];
            $totalItems = count($sourceData);
            
            $generatedContent->update(['total_items' => $totalItems]);

            foreach ($sourceData as $index => $item) {
                try {
                    $generatedFile = $this->generateSingleCarousel(
                        $template,
                        $item,
                        $mappedData,
                        $config,
                        $index
                    );
                    
                    $generatedFiles[] = $generatedFile;
                    
                    $generatedContent->update([
                        'processed_items' => $index + 1,
                        'generated_files' => $generatedFiles,
                    ]);
                    
                } catch (\Exception $e) {
                    // Log individual item error but continue processing
                    \Log::error("Failed to generate carousel item {$index}: " . $e->getMessage());
                }
            }

            $generatedContent->update([
                'status' => 'completed',
                'completed_at' => now(),
                'generated_files' => $generatedFiles,
            ]);

        } catch (\Exception $e) {
            $generatedContent->update([
                'status' => 'failed',
                'error_message' => $e->getMessage(),
                'completed_at' => now(),
            ]);
            
            throw $e;
        }
    }

    /**
     * Generate a single carousel image with text overlay.
     */
    private function generateSingleCarousel(
        Template $template,
        array $itemData,
        array $mappedData,
        array $config,
        int $index
    ): array {
        // Check if image manager is available
        if (!$this->imageManager) {
            throw new \Exception('Image processing library not available. Please install GD or Imagick extension.');
        }

        // Load the template image
        $templatePath = storage_path('app/public/' . $template->file_path);
        $image = $this->imageManager->read($templatePath);

        // Apply text overlays based on mapped data
        foreach ($mappedData as $fieldName => $mapping) {
            if (isset($itemData[$fieldName]) && !empty($mapping['enabled'])) {
                $this->addTextOverlay(
                    $image,
                    $itemData[$fieldName],
                    $mapping,
                    $config
                );
            }
        }

        // Generate unique filename
        $filename = 'generated_' . $template->id . '_' . time() . '_' . $index . '.png';
        $outputPath = "generated/{$template->user_id}/" . $filename;
        $fullOutputPath = storage_path('app/public/' . $outputPath);

        // Create directory if it doesn't exist
        $outputDir = dirname($fullOutputPath);
        if (!is_dir($outputDir)) {
            mkdir($outputDir, 0755, true);
        }

        // Save the generated image
        $image->save($fullOutputPath);

        return [
            'filename' => $filename,
            'path' => $outputPath,
            'url' => Storage::url($outputPath),
            'data' => $itemData,
        ];
    }

    /**
     * Add text overlay to image.
     */
    private function addTextOverlay(
        $image,
        string $text,
        array $mapping,
        array $config
    ): void {
        $x = $mapping['x'] ?? 50;
        $y = $mapping['y'] ?? 50;
        $fontSize = $mapping['font_size'] ?? $config['default_font_size'] ?? 24;
        $fontColor = $mapping['font_color'] ?? $config['default_font_color'] ?? '#000000';
        $fontFamily = $config['font_family'] ?? null;
        $maxWidth = $mapping['max_width'] ?? null;

        // Convert percentage positions to pixels if needed
        if ($x <= 100 && $y <= 100) {
            $imageWidth = $image->width();
            $imageHeight = $image->height();
            $x = ($x / 100) * $imageWidth;
            $y = ($y / 100) * $imageHeight;
        }

        // Handle text wrapping if max width is specified
        if ($maxWidth) {
            $text = $this->wrapText($text, $maxWidth, $fontSize, $fontFamily);
        }

        // Add text to image
        $image->text($text, (int)$x, (int)$y, function (FontFactory $font) use ($fontSize, $fontColor, $fontFamily) {
            $font->size($fontSize);
            $font->color($fontColor);
            
            if ($fontFamily && file_exists($fontFamily)) {
                $font->file($fontFamily);
            }
            
            $font->align('left');
            $font->valign('top');
        });
    }

    /**
     * Wrap text to fit within specified width.
     */
    private function wrapText(string $text, int $maxWidth, int $fontSize, ?string $fontFamily = null): string
    {
        // Simple text wrapping - in a real implementation, you'd want more sophisticated text measurement
        $words = explode(' ', $text);
        $lines = [];
        $currentLine = '';
        
        // Estimate character width (this is a rough approximation)
        $charWidth = $fontSize * 0.6;
        $maxChars = (int)($maxWidth / $charWidth);
        
        foreach ($words as $word) {
            if (strlen($currentLine . ' ' . $word) <= $maxChars) {
                $currentLine .= ($currentLine ? ' ' : '') . $word;
            } else {
                if ($currentLine) {
                    $lines[] = $currentLine;
                }
                $currentLine = $word;
            }
        }
        
        if ($currentLine) {
            $lines[] = $currentLine;
        }
        
        return implode("\n", $lines);
    }

    /**
     * Validate JSON data structure.
     */
    public function validateJsonData(array $data): array
    {
        $errors = [];
        
        if (empty($data)) {
            $errors[] = 'JSON data cannot be empty';
            return $errors;
        }
        
        if (!is_array($data) || !isset($data[0])) {
            $errors[] = 'JSON data must be an array of objects';
            return $errors;
        }
        
        // Check if all items have the same structure
        $firstItemKeys = array_keys($data[0]);
        foreach ($data as $index => $item) {
            if (!is_array($item)) {
                $errors[] = "Item at index {$index} must be an object";
                continue;
            }
            
            $itemKeys = array_keys($item);
            $missingKeys = array_diff($firstItemKeys, $itemKeys);
            $extraKeys = array_diff($itemKeys, $firstItemKeys);
            
            if (!empty($missingKeys)) {
                $errors[] = "Item at index {$index} is missing keys: " . implode(', ', $missingKeys);
            }
            
            if (!empty($extraKeys)) {
                $errors[] = "Item at index {$index} has extra keys: " . implode(', ', $extraKeys);
            }
        }
        
        return $errors;
    }

    /**
     * Get available fields from JSON data.
     */
    public function getAvailableFields(array $data): array
    {
        if (empty($data) || !isset($data[0])) {
            return [];
        }
        
        return array_keys($data[0]);
    }

    /**
     * Generate preview for a single item.
     */
    public function generatePreview(
        Template $template,
        array $itemData,
        array $mappedData,
        array $config
    ): string {
        $generatedFile = $this->generateSingleCarousel(
            $template,
            $itemData,
            $mappedData,
            $config,
            'preview'
        );
        
        return $generatedFile['url'];
    }
}
