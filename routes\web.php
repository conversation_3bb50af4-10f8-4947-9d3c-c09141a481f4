<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\LandingController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\TemplateController;
use App\Http\Controllers\TemplateGroupController;
use App\Http\Controllers\CarouselGeneratorController;
use Illuminate\Support\Facades\Route;

// Landing pages
Route::get('/', [LandingController::class, 'index'])->name('home');
Route::get('/features', [LandingController::class, 'features'])->name('features');
Route::get('/pricing', [LandingController::class, 'pricing'])->name('pricing');
Route::get('/about', [LandingController::class, 'about'])->name('about');

// Contact routes
Route::get('/contact', [ContactController::class, 'index'])->name('contact');
Route::post('/contact', [ContactController::class, 'store'])->name('contact.store');
Route::post('/newsletter', [ContactController::class, 'newsletter'])->name('newsletter.subscribe');
Route::get('/unsubscribe/{token}', [ContactController::class, 'unsubscribe'])->name('newsletter.unsubscribe');

Route::get('/dashboard', [DashboardController::class, 'index'])->middleware(['auth', 'verified'])->name('dashboard');
Route::get('/dashboard/data', [DashboardController::class, 'data'])->middleware(['auth', 'verified'])->name('dashboard.data');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Template Management Routes
    Route::get('/templates', [TemplateController::class, 'index'])->name('templates.index');
    Route::post('/templates/upload', [TemplateController::class, 'upload'])->name('templates.upload');
    Route::patch('/templates/{template}', [TemplateController::class, 'update'])->name('templates.update');
    Route::delete('/templates/{template}', [TemplateController::class, 'destroy'])->name('templates.destroy');
    Route::post('/templates/{template}/favorite', [TemplateController::class, 'toggleFavorite'])->name('templates.favorite');
    Route::post('/templates/{template}/use', [TemplateController::class, 'incrementUsage'])->name('templates.use');
    Route::get('/templates/group/{templateGroup}', [TemplateController::class, 'getGroupTemplates'])->name('templates.group');

    // Template Group Routes
    Route::post('/template-groups', [TemplateGroupController::class, 'store'])->name('template-groups.store');
    Route::post('/template-groups/create-with-files', [TemplateGroupController::class, 'createWithFiles'])->name('template-groups.create-with-files');
    Route::patch('/template-groups/{templateGroup}', [TemplateGroupController::class, 'update'])->name('template-groups.update');
    Route::delete('/template-groups/{templateGroup}', [TemplateGroupController::class, 'destroy'])->name('template-groups.destroy');
    Route::post('/template-groups/reorder', [TemplateGroupController::class, 'reorder'])->name('template-groups.reorder');
    Route::post('/templates/reorder', [TemplateController::class, 'reorderTemplates'])->name('templates.reorder');

    // Carousel Generator Routes
    Route::get('/generator', [CarouselGeneratorController::class, 'index'])->name('generator.index');
    Route::post('/generator/validate-json', [CarouselGeneratorController::class, 'validateJson'])->name('generator.validate-json');
    Route::post('/generator/preview', [CarouselGeneratorController::class, 'generatePreview'])->name('generator.preview');
    Route::post('/generator/generate', [CarouselGeneratorController::class, 'generate'])->name('generator.generate');
    Route::get('/generator/{generatedContent}/status', [CarouselGeneratorController::class, 'getStatus'])->name('generator.status');
    Route::get('/generator/{generatedContent}/download', [CarouselGeneratorController::class, 'download'])->name('generator.download');
    Route::delete('/generator/{generatedContent}', [CarouselGeneratorController::class, 'destroy'])->name('generator.destroy');
});

// Admin routes
Route::middleware(['auth', 'admin'])->group(function () {
    Route::get('/admin', function () {
        return view('admin.dashboard');
    })->name('admin.dashboard');
});

require __DIR__.'/auth.php';
