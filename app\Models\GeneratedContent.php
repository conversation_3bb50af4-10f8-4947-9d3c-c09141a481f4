<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class GeneratedContent extends Model
{
    protected $table = 'generated_content';

    protected $fillable = [
        'user_id',
        'template_id',
        'name',
        'description',
        'source_data',
        'mapped_data',
        'generation_config',
        'status',
        'error_message',
        'total_items',
        'processed_items',
        'generated_files',
        'started_at',
        'completed_at',
    ];

    protected $casts = [
        'source_data' => 'array',
        'mapped_data' => 'array',
        'generation_config' => 'array',
        'generated_files' => 'array',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    /**
     * Get the user that owns the generated content.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the template used for generation.
     */
    public function template(): BelongsTo
    {
        return $this->belongsTo(Template::class);
    }

    /**
     * Get the progress percentage.
     */
    public function getProgressPercentageAttribute(): int
    {
        if ($this->total_items === 0) {
            return 0;
        }

        return round(($this->processed_items / $this->total_items) * 100);
    }

    /**
     * Check if generation is complete.
     */
    public function getIsCompleteAttribute(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if generation failed.
     */
    public function getIsFailedAttribute(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Check if generation is in progress.
     */
    public function getIsProcessingAttribute(): bool
    {
        return $this->status === 'processing';
    }

    /**
     * Get formatted duration.
     */
    public function getDurationAttribute(): ?string
    {
        if (!$this->started_at || !$this->completed_at) {
            return null;
        }

        $duration = $this->completed_at->diffInSeconds($this->started_at);

        if ($duration < 60) {
            return $duration . 's';
        } elseif ($duration < 3600) {
            return round($duration / 60, 1) . 'm';
        } else {
            return round($duration / 3600, 1) . 'h';
        }
    }

    /**
     * Scope to get content for a specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to get content by status.
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get recent content.
     */
    public function scopeRecent($query)
    {
        return $query->orderByDesc('created_at');
    }
}
