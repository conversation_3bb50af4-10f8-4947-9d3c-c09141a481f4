<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\View\View;
use App\Models\Carousel;
use App\Models\UsageTracking;
use App\Models\ActivityLog;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    /**
     * Display the user dashboard
     */
    public function index(): View
    {
        $user = Auth::user();

        // Get user statistics
        $stats = $this->getUserStats($user->id);

        // Get recent carousels
        $recentCarousels = $user->carousels()
            ->orderBy('updated_at', 'desc')
            ->limit(5)
            ->get();

        // Get recent activity
        $recentActivity = ActivityLog::getRecentActivities($user->id, 10);

        // Get usage statistics for the current month
        $monthlyUsage = UsageTracking::getUserStats($user->id, '1 month');

        // Quick actions data
        $quickActions = [
            [
                'title' => 'Create New Carousel',
                'description' => 'Start building your next carousel',
                'icon' => 'plus',
                'route' => 'carousels.create',
                'color' => 'indigo',
            ],
            [
                'title' => 'Browse Templates',
                'description' => 'Explore our template library',
                'icon' => 'template',
                'route' => 'templates.index',
                'color' => 'green',
            ],
            [
                'title' => 'View Analytics',
                'description' => 'Check your carousel performance',
                'icon' => 'chart',
                'route' => 'analytics.index',
                'color' => 'purple',
            ],
            [
                'title' => 'Account Settings',
                'description' => 'Manage your account preferences',
                'icon' => 'settings',
                'route' => 'profile.edit',
                'color' => 'gray',
            ],
        ];

        return view('dashboard.index', compact(
            'user',
            'stats',
            'recentCarousels',
            'recentActivity',
            'monthlyUsage',
            'quickActions'
        ));
    }

    /**
     * Get user statistics
     */
    private function getUserStats(int $userId): array
    {
        $totalCarousels = Carousel::where('user_id', $userId)->count();
        $publishedCarousels = Carousel::where('user_id', $userId)->published()->count();
        $draftCarousels = Carousel::where('user_id', $userId)->draft()->count();
        $totalViews = Carousel::where('user_id', $userId)->sum('views_count');
        $totalExports = Carousel::where('user_id', $userId)->sum('exports_count');

        // Calculate this month's statistics
        $thisMonth = now()->startOfMonth();
        $carouselsThisMonth = Carousel::where('user_id', $userId)
            ->where('created_at', '>=', $thisMonth)
            ->count();

        return [
            'total_carousels' => $totalCarousels,
            'published_carousels' => $publishedCarousels,
            'draft_carousels' => $draftCarousels,
            'total_views' => $totalViews,
            'total_exports' => $totalExports,
            'carousels_this_month' => $carouselsThisMonth,
        ];
    }

    /**
     * Get dashboard data for API
     */
    public function data(Request $request)
    {
        $user = Auth::user();

        return response()->json([
            'stats' => $this->getUserStats($user->id),
            'recent_carousels' => $user->carousels()
                ->orderBy('updated_at', 'desc')
                ->limit(5)
                ->get(),
            'recent_activity' => ActivityLog::getRecentActivities($user->id, 10),
        ]);
    }
}
