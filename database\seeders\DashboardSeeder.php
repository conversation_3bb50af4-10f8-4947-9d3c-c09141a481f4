<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Carousel;
use App\Models\ActivityLog;
use App\Models\UsageTracking;

class DashboardSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the admin user
        $adminUser = User::where('email', '<EMAIL>')->first();

        if (!$adminUser) {
            return;
        }

        // Create sample carousels
        $carousels = [
            [
                'title' => 'Social Media Marketing Tips',
                'description' => 'A carousel showcasing effective social media marketing strategies',
                'status' => 'published',
                'content' => [
                    'slides' => [
                        ['title' => 'Tip 1', 'content' => 'Post consistently'],
                        ['title' => 'Tip 2', 'content' => 'Engage with your audience'],
                        ['title' => 'Tip 3', 'content' => 'Use relevant hashtags'],
                    ]
                ],
                'views_count' => 150,
                'exports_count' => 5,
                'published_at' => now()->subDays(2),
            ],
            [
                'title' => 'Product Launch Announcement',
                'description' => 'Announcing our new product features',
                'status' => 'published',
                'content' => [
                    'slides' => [
                        ['title' => 'New Features', 'content' => 'Introducing amazing new capabilities'],
                        ['title' => 'Benefits', 'content' => 'How these features help you'],
                        ['title' => 'Get Started', 'content' => 'Try them today'],
                    ]
                ],
                'views_count' => 89,
                'exports_count' => 3,
                'published_at' => now()->subDays(1),
            ],
            [
                'title' => 'Company Culture Showcase',
                'description' => 'Highlighting our amazing team and culture',
                'status' => 'draft',
                'content' => [
                    'slides' => [
                        ['title' => 'Our Team', 'content' => 'Meet the amazing people'],
                        ['title' => 'Our Values', 'content' => 'What drives us'],
                    ]
                ],
                'views_count' => 0,
                'exports_count' => 0,
            ],
        ];

        foreach ($carousels as $carouselData) {
            Carousel::create(array_merge($carouselData, ['user_id' => $adminUser->id]));
        }

        // Create sample activity logs
        $activities = [
            [
                'action' => 'carousel_created',
                'description' => 'Created a new carousel "Social Media Marketing Tips"',
                'created_at' => now()->subDays(2),
            ],
            [
                'action' => 'carousel_published',
                'description' => 'Published carousel "Social Media Marketing Tips"',
                'created_at' => now()->subDays(2)->addHours(1),
            ],
            [
                'action' => 'carousel_created',
                'description' => 'Created a new carousel "Product Launch Announcement"',
                'created_at' => now()->subDays(1),
            ],
            [
                'action' => 'carousel_published',
                'description' => 'Published carousel "Product Launch Announcement"',
                'created_at' => now()->subDays(1)->addHours(2),
            ],
            [
                'action' => 'carousel_created',
                'description' => 'Created a new carousel "Company Culture Showcase"',
                'created_at' => now()->subHours(3),
            ],
            [
                'action' => 'login',
                'description' => 'Logged into the dashboard',
                'created_at' => now()->subHours(1),
            ],
        ];

        foreach ($activities as $activityData) {
            ActivityLog::create(array_merge($activityData, [
                'user_id' => $adminUser->id,
                'ip_address' => '127.0.0.1',
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            ]));
        }

        // Create sample usage tracking
        $usageData = [
            ['action' => 'carousel_created', 'tracked_at' => now()->subDays(2)],
            ['action' => 'carousel_published', 'tracked_at' => now()->subDays(2)->addHours(1)],
            ['action' => 'carousel_exported', 'tracked_at' => now()->subDays(2)->addHours(2)],
            ['action' => 'carousel_created', 'tracked_at' => now()->subDays(1)],
            ['action' => 'carousel_published', 'tracked_at' => now()->subDays(1)->addHours(2)],
            ['action' => 'carousel_created', 'tracked_at' => now()->subHours(3)],
            ['action' => 'template_used', 'tracked_at' => now()->subHours(2)],
        ];

        foreach ($usageData as $usage) {
            UsageTracking::create(array_merge($usage, ['user_id' => $adminUser->id]));
        }
    }
}
