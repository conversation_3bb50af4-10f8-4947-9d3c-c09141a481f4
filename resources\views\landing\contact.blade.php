<!DOCTYPE html>
<html lang="en" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us - CarouselGen</title>
    <meta name="description" content="Get in touch with the CarouselGen team. We're here to help with any questions or feedback.">
    
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    <script>
        // Prevent flash of unstyled content
        if (localStorage.getItem('theme') === 'dark' || (!localStorage.getItem('theme') && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            document.documentElement.classList.add('dark');
        }
    </script>
</head>
<body class="app-background">
    <!-- Navigation -->
    <nav class="card-primary border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="{{ route('home') }}" class="flex items-center">
                        <div class="h-8 w-8 bg-indigo-600 rounded-lg flex items-center justify-center">
                            <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <span class="ml-2 text-xl font-bold text-primary">CarouselGen</span>
                    </a>
                </div>
                <div class="flex items-center space-x-4">
                    <x-theme-toggle />
                    <a href="{{ route('home') }}" class="text-secondary hover:text-indigo-600 px-3 py-2 text-sm font-medium">← Back to Home</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Contact Section -->
    <section class="py-24">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="max-w-2xl mx-auto text-center">
                <h1 class="text-4xl font-bold text-primary sm:text-5xl">
                    Get in Touch
                </h1>
                <p class="mt-4 text-xl text-secondary">
                    Have a question or feedback? We'd love to hear from you. Send us a message and we'll respond as soon as possible.
                </p>
            </div>

            <div class="mt-20 grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Contact Info -->
                <div class="lg:col-span-1">
                    <div class="card-primary p-6">
                        <h3 class="text-lg font-semibold text-primary mb-6">Contact Information</h3>
                        
                        <div class="space-y-6">
                            <div class="flex items-start">
                                <svg class="h-6 w-6 text-indigo-600 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-primary">Email</div>
                                    <div class="text-sm text-secondary"><EMAIL></div>
                                </div>
                            </div>

                            <div class="flex items-start">
                                <svg class="h-6 w-6 text-indigo-600 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-primary">Office</div>
                                    <div class="text-sm text-secondary">San Francisco, CA</div>
                                </div>
                            </div>

                            <div class="flex items-start">
                                <svg class="h-6 w-6 text-indigo-600 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-primary">Response Time</div>
                                    <div class="text-sm text-secondary">Within 24 hours</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Form -->
                <div class="lg:col-span-2">
                    <div class="card-primary p-8">
                        @if(session('success'))
                            <div class="mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                                <div class="flex">
                                    <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                    </svg>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-green-800 dark:text-green-200">
                                            {{ session('success') }}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        @endif

                        @if($errors->has('general') || $errors->has('rate_limit'))
                            <div class="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                                <div class="flex">
                                    <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                    </svg>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-red-800 dark:text-red-200">
                                            {{ $errors->first('general') ?: $errors->first('rate_limit') }}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        @endif

                        <form action="{{ route('contact.store') }}" method="POST" class="space-y-6" x-data="contactForm()">
                            @csrf
                            
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                                <div>
                                    <label for="name" class="block text-sm font-medium text-primary mb-1">
                                        Name *
                                    </label>
                                    <input type="text" id="name" name="name" required
                                           class="input-field @error('name') border-red-500 dark:border-red-400 @enderror"
                                           value="{{ old('name') }}"
                                           placeholder="Your full name">
                                    @error('name')
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="email" class="block text-sm font-medium text-primary mb-1">
                                        Email *
                                    </label>
                                    <input type="email" id="email" name="email" required
                                           class="input-field @error('email') border-red-500 dark:border-red-400 @enderror"
                                           value="{{ old('email') }}"
                                           placeholder="<EMAIL>">
                                    @error('email')
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>

                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                                <div>
                                    <label for="phone" class="block text-sm font-medium text-primary mb-1">
                                        Phone
                                    </label>
                                    <input type="tel" id="phone" name="phone"
                                           class="input-field @error('phone') border-red-500 dark:border-red-400 @enderror"
                                           value="{{ old('phone') }}"
                                           placeholder="+****************">
                                    @error('phone')
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="company" class="block text-sm font-medium text-primary mb-1">
                                        Company
                                    </label>
                                    <input type="text" id="company" name="company"
                                           class="input-field @error('company') border-red-500 dark:border-red-400 @enderror"
                                           value="{{ old('company') }}"
                                           placeholder="Your company name">
                                    @error('company')
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>

                            <div>
                                <label for="subject" class="block text-sm font-medium text-primary mb-1">
                                    Subject
                                </label>
                                <input type="text" id="subject" name="subject"
                                       class="input-field @error('subject') border-red-500 dark:border-red-400 @enderror"
                                       value="{{ old('subject') }}"
                                       placeholder="What's this about?">
                                @error('subject')
                                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="message" class="block text-sm font-medium text-primary mb-1">
                                    Message *
                                </label>
                                <textarea id="message" name="message" rows="6" required
                                          class="input-field @error('message') border-red-500 dark:border-red-400 @enderror"
                                          placeholder="Tell us more about your inquiry...">{{ old('message') }}</textarea>
                                @error('message')
                                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <button type="submit" class="btn-primary w-full py-3 px-6" :disabled="loading" x-text="loading ? 'Sending...' : 'Send Message'">
                                    Send Message
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script>
        function contactForm() {
            return {
                loading: false,
                
                async submit() {
                    this.loading = true;
                    // Form will submit normally, this is just for UI feedback
                }
            }
        }
    </script>
</body>
</html>
