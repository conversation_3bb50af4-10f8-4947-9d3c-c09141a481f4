<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="h-full">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'CarouselGen') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    <script>
        // Prevent flash of unstyled content
        if (localStorage.getItem('theme') === 'dark' || (!localStorage.getItem('theme') && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            document.documentElement.classList.add('dark');
        }
    </script>
</head>
<body class="h-full app-background" x-data="dashboardApp()">
    <div>
        <!-- Off-canvas menu for mobile -->
        <div class="relative z-50 lg:hidden" role="dialog" aria-modal="true" x-show="sidebarOpen" x-transition>
            <!-- Backdrop -->
            <div class="fixed inset-0 bg-gray-900/80" aria-hidden="true" x-show="sidebarOpen" 
                 x-transition:enter="transition-opacity ease-linear duration-300"
                 x-transition:enter-start="opacity-0"
                 x-transition:enter-end="opacity-100"
                 x-transition:leave="transition-opacity ease-linear duration-300"
                 x-transition:leave-start="opacity-100"
                 x-transition:leave-end="opacity-0"></div>

            <div class="fixed inset-0 flex">
                <!-- Off-canvas menu -->
                <div class="relative mr-16 flex w-full max-w-xs flex-1" x-show="sidebarOpen"
                     x-transition:enter="transition ease-in-out duration-300 transform"
                     x-transition:enter-start="-translate-x-full"
                     x-transition:enter-end="translate-x-0"
                     x-transition:leave="transition ease-in-out duration-300 transform"
                     x-transition:leave-start="translate-x-0"
                     x-transition:leave-end="-translate-x-full">
                    
                    <!-- Close button -->
                    <div class="absolute top-0 left-full flex w-16 justify-center pt-5">
                        <button type="button" class="-m-2.5 p-2.5" @click="sidebarOpen = false">
                            <span class="sr-only">Close sidebar</span>
                            <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    <!-- Sidebar component -->
                    <div class="flex grow flex-col gap-y-5 overflow-y-auto card-primary px-6 pb-2">
                        <div class="flex h-16 shrink-0 items-center">
                            <div class="h-8 w-8 bg-indigo-600 rounded-lg flex items-center justify-center">
                                <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <span class="ml-2 text-xl font-bold text-primary">CarouselGen</span>
                        </div>
                        
                        @include('layouts.dashboard-navigation')
                    </div>
                </div>
            </div>
        </div>

        <!-- Static sidebar for desktop -->
        <div class="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col">
            <div class="flex grow flex-col gap-y-5 overflow-y-auto border-r border-gray-200 dark:border-gray-700 card-primary px-6">
                <div class="flex h-16 shrink-0 items-center">
                    <div class="h-8 w-8 bg-indigo-600 rounded-lg flex items-center justify-center">
                        <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <span class="ml-2 text-xl font-bold text-primary">CarouselGen</span>
                </div>
                
                @include('layouts.dashboard-navigation')
            </div>
        </div>

        <!-- Mobile header -->
        <div class="sticky top-0 z-40 flex items-center gap-x-6 card-primary px-4 py-4 shadow-sm sm:px-6 lg:hidden">
            <button type="button" class="-m-2.5 p-2.5 text-secondary lg:hidden" @click="sidebarOpen = true">
                <span class="sr-only">Open sidebar</span>
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
                </svg>
            </button>
            <div class="flex-1 text-sm font-semibold text-primary">
                @isset($header)
                    {{ $header }}
                @else
                    Dashboard
                @endisset
            </div>
            
            <!-- Theme Toggle -->
            <x-theme-toggle />
            
            <!-- User menu -->
            <div class="relative" x-data="{ open: false }">
                <button type="button" class="flex items-center gap-x-4 px-6 py-3 text-sm font-semibold text-primary hover:bg-gray-50 dark:hover:bg-gray-800" @click="open = !open">
                    <img class="h-8 w-8 rounded-full bg-gray-50" src="https://ui-avatars.com/api/?name={{ urlencode(auth()->user()->name) }}&color=7c3aed&background=ede9fe" alt="{{ auth()->user()->name }}">
                    <span class="sr-only">Your profile</span>
                    <span aria-hidden="true">{{ auth()->user()->name }}</span>
                </button>
                
                <div x-show="open" @click.away="open = false" x-transition
                     class="absolute right-0 z-10 mt-2 w-48 origin-top-right card-primary shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                    <div class="py-1">
                        <a href="{{ route('profile.edit') }}" class="block px-4 py-2 text-sm text-secondary hover:bg-gray-100 dark:hover:bg-gray-700">Profile</a>
                        <a href="#" class="block px-4 py-2 text-sm text-secondary hover:bg-gray-100 dark:hover:bg-gray-700">Settings</a>
                        <form method="POST" action="{{ route('logout') }}">
                            @csrf
                            <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-secondary hover:bg-gray-100 dark:hover:bg-gray-700">
                                Log Out
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main content -->
        <main class="py-10 lg:pl-72">
            <div class="px-4 sm:px-6 lg:px-8">
                @isset($header)
                    <div class="mb-8">
                        <h1 class="text-2xl font-bold text-primary">{{ $header }}</h1>
                    </div>
                @endisset
                
                {{ $slot }}
            </div>
        </main>
    </div>

    <script>
        function dashboardApp() {
            return {
                sidebarOpen: false,
                
                init() {
                    // Initialize dashboard functionality
                }
            }
        }
    </script>
</body>
</html>
