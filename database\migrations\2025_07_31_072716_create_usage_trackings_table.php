<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('usage_trackings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('action'); // 'carousel_created', 'carousel_exported', 'template_used', etc.
            $table->string('resource_type')->nullable(); // 'carousel', 'template', etc.
            $table->unsignedBigInteger('resource_id')->nullable();
            $table->json('metadata')->nullable(); // Additional data about the action
            $table->timestamp('tracked_at')->useCurrent();
            $table->timestamps();

            $table->index(['user_id', 'action']);
            $table->index(['user_id', 'tracked_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('usage_trackings');
    }
};
