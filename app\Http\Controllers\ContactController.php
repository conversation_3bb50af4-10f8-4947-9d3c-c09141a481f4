<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use App\Models\ContactSubmission;
use App\Models\NewsletterSubscription;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Mail;

class ContactController extends Controller
{
    /**
     * Display the contact page
     */
    public function index(): View
    {
        return view('landing.contact');
    }

    /**
     * Handle contact form submission
     */
    public function store(Request $request): RedirectResponse|JsonResponse
    {
        // Rate limiting
        $key = 'contact-form:' . $request->ip();
        if (RateLimiter::tooManyAttempts($key, 5)) {
            $seconds = RateLimiter::availableIn($key);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => "Too many attempts. Please try again in {$seconds} seconds.",
                ], 429);
            }

            return back()->withErrors([
                'rate_limit' => "Too many attempts. Please try again in {$seconds} seconds.",
            ]);
        }

        // Validation
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'nullable|string|max:255',
            'message' => 'required|string|max:5000',
            'phone' => 'nullable|string|max:20',
            'company' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            return back()->withErrors($validator)->withInput();
        }

        try {
            // Create contact submission
            $submission = ContactSubmission::create($validator->validated());

            // Send notification email (you can implement this later)
            // Mail::to(config('mail.contact_email'))->send(new ContactFormSubmitted($submission));

            // Hit rate limiter
            RateLimiter::hit($key, 300); // 5 minutes

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Thank you for your message! We\'ll get back to you soon.',
                ]);
            }

            return back()->with('success', 'Thank you for your message! We\'ll get back to you soon.');

        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Something went wrong. Please try again later.',
                ], 500);
            }

            return back()->withErrors([
                'general' => 'Something went wrong. Please try again later.',
            ])->withInput();
        }
    }

    /**
     * Handle newsletter subscription
     */
    public function newsletter(Request $request): RedirectResponse|JsonResponse
    {
        // Rate limiting
        $key = 'newsletter:' . $request->ip();
        if (RateLimiter::tooManyAttempts($key, 3)) {
            $seconds = RateLimiter::availableIn($key);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => "Too many attempts. Please try again in {$seconds} seconds.",
                ], 429);
            }

            return back()->withErrors([
                'newsletter_rate_limit' => "Too many attempts. Please try again in {$seconds} seconds.",
            ]);
        }

        // Validation
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|max:255',
            'name' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Please provide a valid email address.',
                    'errors' => $validator->errors(),
                ], 422);
            }

            return back()->withErrors($validator)->withInput();
        }

        try {
            // Check if already subscribed
            $existing = NewsletterSubscription::where('email', $request->email)->first();

            if ($existing) {
                if ($existing->isActive()) {
                    $message = 'You\'re already subscribed to our newsletter!';
                } else {
                    $existing->resubscribe();
                    $message = 'Welcome back! You\'ve been resubscribed to our newsletter.';
                }
            } else {
                NewsletterSubscription::create($validator->validated());
                $message = 'Thank you for subscribing to our newsletter!';
            }

            // Hit rate limiter
            RateLimiter::hit($key, 300); // 5 minutes

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => $message,
                ]);
            }

            return back()->with('newsletter_success', $message);

        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Something went wrong. Please try again later.',
                ], 500);
            }

            return back()->withErrors([
                'newsletter_general' => 'Something went wrong. Please try again later.',
            ])->withInput();
        }
    }

    /**
     * Handle newsletter unsubscribe
     */
    public function unsubscribe(Request $request, string $token): View
    {
        $subscription = NewsletterSubscription::where('unsubscribe_token', $token)->first();

        if (!$subscription) {
            abort(404, 'Invalid unsubscribe link.');
        }

        if ($subscription->status === 'unsubscribed') {
            return view('landing.unsubscribe', [
                'message' => 'You have already been unsubscribed from our newsletter.',
                'already_unsubscribed' => true,
            ]);
        }

        $subscription->unsubscribe();

        return view('landing.unsubscribe', [
            'message' => 'You have been successfully unsubscribed from our newsletter.',
            'already_unsubscribed' => false,
        ]);
    }
}
