## PROMPT 5 OF 12: JSON DATA PREPARATION & UPLOAD WORKFLOW

### CONTEXT:
This is prompt 5 of 12 in the development sequence. We are implementing the comprehensive JSON data preparation and upload workflow that serves as the foundation for carousel generation. This system handles structured JSON data containing text content to be overlaid on templates, with robust validation and user guidance.

### WHAT TO DO:
**Implementation Order (MANDATORY):**
1. **Backend**: Create JsonDataService, validation engine, and structured data storage
2. **Frontend**: Build comprehensive JSON upload interface with drag-drop and validation feedback using DaisyUI
3. **Integration**: Connect JSON parsing with real-time validation and key extraction using Alpine.js
4. **Testing**: Validate complete JSON workflow including error handling and data extraction

### FEATURE: JSON Data Preparation and Upload System
### TECH: Laravel JSON validation + DaisyUI file input, alert, table, progress + Alpine.js
### UI: JSON upload with drag-drop, validation feedback, key extraction display using DaisyUI
### THEME: Forest theme with DaisyUI file input, alert, table, and progress components
### LOGIC: JSON structure validation, key extraction, data consistency checking, error reporting
### DATA: JSON data storage with metadata, validation logs, extracted key mappings
### ERROR HANDLING: Comprehensive JSON validation with detailed error messages and correction suggestions

### JSON STRUCTURE REQUIREMENTS:
The system must validate JSON files containing:
```json
{
  "templates": [
    {
      "id": "template_1",
      "slides": [
        {"slide_1_content": "value", "slide_2_content": "value"},
        {"slide_1_content": "value", "slide_2_content": "value"}
      ],
      "caption": "Social media caption text"
    }
  ]
}
```

### OUTPUT REQUIREMENTS:
- Drag-and-drop JSON file upload with format validation
- Real-time JSON structure validation with detailed error reporting
- Automatic key extraction and display with sample values
- Data consistency validation (slide numbers vs template slides)
- Preview of extracted data structure before mapping
- Error correction suggestions and re-upload capability

### SUCCESS CRITERIA:
- [ ] JSON files upload via drag-drop interface
- [ ] Comprehensive validation checks structure and data types
- [ ] Detailed error messages guide users to fix JSON issues
- [ ] Key extraction displays all available data fields
- [ ] Sample values help users understand data structure
- [ ] Forest theme applied consistently
- [ ] Alpine.js handles real-time validation feedback

---

## PROMPT 6 OF 12: TEMPLATE MAPPING & CUSTOMIZATION WORKFLOW

### CONTEXT:
This is prompt 6 of 12 in the development sequence. We are implementing the sophisticated template mapping and customization system that connects JSON data with visual template elements through an intuitive drag-and-drop interface with real-time preview capabilities.

### WHAT TO DO:
**Implementation Order (MANDATORY):**
1. **Backend**: Create TemplateMappingService, element positioning system, and mapping validation
2. **Frontend**: Build drag-drop mapping interface with live preview and customization controls using DaisyUI
3. **Integration**: Connect mapping system with real-time preview updates using Alpine.js
4. **Testing**: Validate mapping functionality, customization controls, and preview accuracy

### FEATURE: Template Mapping and Customization System
### TECH: Laravel mapping engine + DaisyUI drag-drop, slider, color picker, preview + Alpine.js
### UI: Split-view interface with template preview and data panel using DaisyUI
### THEME: Forest theme with DaisyUI drag-drop, form controls, and preview components
### LOGIC: Drag-drop mapping, text overlay positioning, font customization, preview generation
### DATA: Template mapping configuration, element positioning, styling preferences
### ERROR HANDLING: Mapping validation, overlay conflicts, positioning errors with visual feedback

### MAPPING INTERFACE REQUIREMENTS:
- Split-view layout: template preview on left, JSON keys panel on right
- Drag-and-drop functionality for mapping keys to template areas
- Real-time text overlay preview with sample data
- Customization controls for font size, color, alignment, and positioning
- Multi-slide mapping support with consistent key application
- Preview cycling through different JSON data entries

### CUSTOMIZATION FEATURES:
- Font size adjustment with live preview
- Color picker for text overlays
- Text alignment options (left, center, right)
- Position fine-tuning with drag handles
- Style presets for quick application
- Mapping validation to prevent overlaps

### OUTPUT REQUIREMENTS:
- Intuitive drag-drop interface for key-to-element mapping
- Real-time preview showing text overlays on templates
- Comprehensive customization controls with live updates
- Multi-entry preview to validate mappings across data variations
- Mapping validation with conflict detection and resolution
- Save/load mapping configurations for reuse

### SUCCESS CRITERIA:
- [ ] Drag-drop mapping works smoothly between panels
- [ ] Real-time preview updates with text overlays
- [ ] Customization controls modify overlays instantly
- [ ] Multi-entry preview validates mapping consistency
- [ ] Mapping conflicts detected and resolved
- [ ] Forest theme applied to all interface elements
- [ ] Alpine.js handles all interactive mapping features

---

## PROMPT 7 OF 12: CAROUSEL GENERATION WORKFLOW

### CONTEXT:
This is prompt 7 of 12 in the development sequence. We are implementing the comprehensive carousel generation engine that combines templates, JSON data, and mappings to create high-quality Instagram carousels with server-side image processing and batch generation capabilities.

### WHAT TO DO:
**Implementation Order (MANDATORY):**
1. **Backend**: Create CarouselGeneratorService, image processing engine, and batch generation system
2. **Frontend**: Build generation interface with progress tracking and preview using DaisyUI
3. **Integration**: Connect generation process with real-time progress updates using Alpine.js
4. **Testing**: Validate generation quality, batch processing, and download functionality

### FEATURE: Carousel Generation Engine
### TECH: Intervention Image + Laravel Queues + DaisyUI progress, modal, grid + Alpine.js
### UI: Generation interface with progress tracking, preview, and download options using DaisyUI
### THEME: Forest theme with DaisyUI progress, modal, and grid components
### LOGIC: Server-side image processing, text overlay rendering, batch generation, quality optimization
### DATA: Generated carousel storage with metadata, processing status, and file management
### ERROR HANDLING: Generation failures, image processing errors, validation issues with detailed feedback

### GENERATION PROCESS REQUIREMENTS:
- Mapping validation before generation starts
- Server-side image processing for consistent quality
- High-resolution output (1080x1080 pixels for Instagram)
- Batch processing for multiple JSON entries
- Progress tracking with real-time updates
- Individual slide and complete carousel generation

### IMAGE PROCESSING FEATURES:
- Text overlay rendering with custom fonts and styling
- Position-accurate text placement based on mappings
- Color and transparency handling for text overlays
- Image optimization for social media platforms
- Multiple format support (PNG, JPG) with quality settings
- Batch processing with queue management

### OUTPUT REQUIREMENTS:
- Real-time generation progress with detailed status updates
- Preview of generated carousels before download
- Individual slide download and complete carousel sets
- Batch download functionality for multiple carousels
- PDF export option for review and approval workflows
- Unique identifier assignment for content tracking

### SUCCESS CRITERIA:
- [ ] Generation validates all mappings before processing
- [ ] Server-side processing produces high-quality images
- [ ] Batch generation handles multiple JSON entries efficiently
- [ ] Progress tracking provides real-time status updates
- [ ] Download options work for individual and batch content
- [ ] Forest theme applied to all generation interfaces
- [ ] Alpine.js manages generation interactions and progress updates

---

## PROMPT 8 OF 12: SOCIAL MEDIA INTEGRATION

### CONTEXT:
This is prompt 8 of 12 in the development sequence. At this stage, we are implementing social media account connections which allow users to connect their Instagram and Facebook accounts for automated posting.

### WHAT TO DO:
**Implementation Order (MANDATORY):**
1. **Backend**: Create SocialAccount model, OAuth integration, API connections
2. **Frontend**: Build account connection interface using DaisyUI components
3. **Integration**: Connect OAuth flow with account management using Alpine.js
4. **Testing**: Validate social media account connections work

### FEATURE: Social Media Account Integration
### TECH: Laravel Socialite + DaisyUI card, button, badge components + Alpine.js
### UI: Account connection cards, OAuth flow, connection status using DaisyUI
### THEME: Forest theme with DaisyUI card and button components
### LOGIC: OAuth authentication, token storage, account validation
### DATA: SocialAccount table with platform credentials
### ERROR HANDLING: OAuth failures, API connection errors with clear feedback

### OUTPUT REQUIREMENTS:
- Social media platform connection cards
- OAuth authentication flow for Instagram and Facebook
- Connection status indicators and management
- Account disconnection with confirmation
- Platform-specific settings and permissions

### SUCCESS CRITERIA:
- [ ] Instagram Business accounts can be connected
- [ ] Facebook Pages can be connected
- [ ] OAuth flow works without errors
- [ ] Connection status displays accurately
- [ ] Forest theme applied to all components
- [ ] Alpine.js OAuth interactions working

---

## PROMPT 9 OF 12: SCHEDULER SYSTEM

### CONTEXT:
This is prompt 9 of 12 in the development sequence. At this stage, we are implementing the post scheduling system which allows users to schedule carousel posts with date ranges, auto-spacing, and caption mapping.

### WHAT TO DO:
**Implementation Order (MANDATORY):**
1. **Backend**: Create ScheduledPost model, scheduling logic, queue system
2. **Frontend**: Build scheduling interface with calendar and settings using DaisyUI
3. **Integration**: Connect scheduling controls with calendar view using Alpine.js
4. **Testing**: Validate scheduling functionality and queue processing

### FEATURE: Post Scheduling System
### TECH: Laravel Queues + DaisyUI calendar, form, timeline components + Alpine.js
### UI: Scheduling interface with calendar view, date range picker using DaisyUI
### THEME: Forest theme with DaisyUI calendar and form components
### LOGIC: Schedule calculation, auto-spacing, queue job processing
### DATA: ScheduledPost table with timing and status tracking
### ERROR HANDLING: Scheduling conflicts, queue failures with user notifications

### OUTPUT REQUIREMENTS:
- Calendar view for scheduled posts
- Date range scheduling with auto-spacing
- Content source selection (upload or library)
- Caption mapping from JSON data
- Schedule management and editing

### SUCCESS CRITERIA:
- [ ] Posts can be scheduled with date ranges
- [ ] Auto-spacing calculates optimal timing
- [ ] Calendar view shows scheduled posts
- [ ] Content sources work properly
- [ ] Forest theme applied to scheduling interface
- [ ] Alpine.js calendar interactions working

---

## PROMPT 10 OF 12: ANALYTICS DASHBOARD

### CONTEXT:
This is prompt 10 of 12 in the development sequence. At this stage, we are implementing the analytics dashboard which provides users with insights into their template usage, generation statistics, and posting performance.

### WHAT TO DO:
**Implementation Order (MANDATORY):**
1. **Backend**: Create analytics service, data aggregation, metrics calculation
2. **Frontend**: Build analytics dashboard with charts and metrics using DaisyUI
3. **Integration**: Connect real-time data updates with Alpine.js
4. **Testing**: Validate analytics data accuracy and dashboard functionality

### FEATURE: Analytics and Performance Dashboard
### TECH: Laravel analytics + DaisyUI stats, chart, table components + Alpine.js
### UI: Analytics dashboard with charts, metrics, and data tables using DaisyUI
### THEME: Forest theme with DaisyUI stats and chart components
### LOGIC: Data aggregation, metrics calculation, performance tracking
### DATA: Analytics table with user metrics and timestamps
### ERROR HANDLING: Data loading errors, calculation failures with fallbacks

### OUTPUT REQUIREMENTS:
- Usage statistics with visual charts
- Template performance metrics
- Generation and posting analytics
- Exportable analytics reports
- Real-time data updates

### SUCCESS CRITERIA:
- [ ] Analytics display accurate user data
- [ ] Charts render properly with forest theme
- [ ] Metrics calculate correctly
- [ ] Data exports work as expected
- [ ] Forest theme applied to all analytics components
- [ ] Alpine.js data updates working

---

## PROMPT 11 OF 12: BILLING SYSTEM

### CONTEXT:
This is prompt 11 of 12 in the development sequence. At this stage, we are implementing the subscription billing system which manages user plans, usage limits, and payment processing for the SaaS platform.

### WHAT TO DO:
**Implementation Order (MANDATORY):**
1. **Backend**: Create subscription models, Stripe integration, usage tracking
2. **Frontend**: Build billing interface with plan management using DaisyUI
3. **Integration**: Connect payment flow with subscription management using Alpine.js
4. **Testing**: Validate billing functionality and subscription management

### FEATURE: Subscription Billing System
### TECH: Laravel Cashier + DaisyUI pricing, modal, alert components + Alpine.js
### UI: Billing dashboard with plan comparison, payment forms using DaisyUI
### THEME: Forest theme with DaisyUI pricing and form components
### LOGIC: Subscription management, usage tracking, payment processing
### DATA: Subscription tables with plan details and usage limits
### ERROR HANDLING: Payment failures, subscription errors with user guidance

### OUTPUT REQUIREMENTS:
- Subscription plan overview and management
- Usage tracking with limit indicators
- Payment method management
- Invoice history and downloads
- Plan upgrade/downgrade functionality

### SUCCESS CRITERIA:
- [ ] Subscription plans display correctly
- [ ] Payment processing works without errors
- [ ] Usage limits enforce properly
- [ ] Invoice generation works
- [ ] Forest theme applied to billing interface
- [ ] Alpine.js payment interactions working

---

## PROMPT 12 OF 12: ADMIN DASHBOARD

### CONTEXT:
This is prompt 12 of 12 in the development sequence. At this stage, we are implementing the admin dashboard which provides system administrators with user management, analytics, and system monitoring capabilities.

### WHAT TO DO:
**Implementation Order (MANDATORY):**
1. **Backend**: Create admin controllers, user management, system analytics
2. **Frontend**: Build admin interface with user management using DaisyUI
3. **Integration**: Connect admin controls with data management using Alpine.js
4. **Testing**: Validate admin functionality and access controls

### FEATURE: Admin Dashboard and User Management
### TECH: Laravel admin + DaisyUI admin components, tables, modals + Alpine.js
### UI: Admin dashboard with user management, system metrics using DaisyUI
### THEME: Forest theme with DaisyUI admin-focused components
### LOGIC: User management, system monitoring, admin analytics
### DATA: Admin views of all user data and system metrics
### ERROR HANDLING: Admin operation errors with detailed logging

### OUTPUT REQUIREMENTS:
- Comprehensive admin dashboard with system overview
- User management with account controls
- System analytics and monitoring
- Content moderation capabilities
- Admin-specific navigation and permissions

### SUCCESS CRITERIA:
- [ ] Admin dashboard loads with system metrics
- [ ] User management operations work properly
- [ ] System analytics display accurately
- [ ] Admin permissions enforce correctly
- [ ] Forest theme applied to admin interface
- [ ] Alpine.js admin interactions working

### TESTING PHASE (MANDATORY):
- Test functionality on mobile (320px) and desktop (1024px+) viewports
- Verify all DaisyUI components are properly implemented using MCP server documentation
- Test error handling and validation scenarios
- Confirm Alpine.js interactivity works properly
- Validate MySQL database operations

### ADDITIONAL RULES:
- **PRIORITY 1**: Use DaisyUI MCP server for component discovery and documentation
- **PRIORITY 2**: Use existing libraries/packages when they meet the requirements
- **PRIORITY 3**: Only code from scratch when no suitable library exists
- Must use forest theme with DaisyUI theme system
- Must use ONLY DaisyUI components (NO CUSTOM COMPONENTS)
- Must follow Laravel conventions
