<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\TemplateGroup;
use App\Models\Template;

class TemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the admin user
        $adminUser = User::where('email', '<EMAIL>')->first();

        if (!$adminUser) {
            return;
        }

        // Create sample template groups
        $socialMediaGroup = TemplateGroup::create([
            'user_id' => $adminUser->id,
            'name' => 'Social Media',
            'description' => 'Templates for social media posts and stories',
            'color' => '#3b82f6',
            'sort_order' => 1,
        ]);

        $businessGroup = TemplateGroup::create([
            'user_id' => $adminUser->id,
            'name' => 'Business',
            'description' => 'Professional business presentation templates',
            'color' => '#10b981',
            'sort_order' => 2,
        ]);

        $marketingGroup = TemplateGroup::create([
            'user_id' => $adminUser->id,
            'name' => 'Marketing',
            'description' => 'Marketing and promotional templates',
            'color' => '#f59e0b',
            'sort_order' => 3,
        ]);

        // Create sample templates
        $templates = [
            [
                'template_group_id' => $socialMediaGroup->id,
                'name' => 'Instagram Story Template',
                'description' => 'Modern Instagram story template with gradient background',
                'file_name' => 'instagram-story-template.png',
                'file_type' => 'image/png',
                'file_size' => 245760, // 240KB
                'metadata' => [
                    'width' => 1080,
                    'height' => 1920,
                    'aspect_ratio' => 0.56,
                    'extension' => 'png'
                ],
                'is_favorite' => true,
                'usage_count' => 5,
                'last_used_at' => now()->subDays(1),
            ],
            [
                'template_group_id' => $socialMediaGroup->id,
                'name' => 'Facebook Post Template',
                'description' => 'Eye-catching Facebook post template',
                'file_name' => 'facebook-post-template.jpg',
                'file_type' => 'image/jpeg',
                'file_size' => 189440, // 185KB
                'metadata' => [
                    'width' => 1200,
                    'height' => 630,
                    'aspect_ratio' => 1.9,
                    'extension' => 'jpg'
                ],
                'usage_count' => 3,
                'last_used_at' => now()->subDays(3),
            ],
            [
                'template_group_id' => $businessGroup->id,
                'name' => 'Business Presentation',
                'description' => 'Clean and professional business presentation template',
                'file_name' => 'business-presentation.pdf',
                'file_type' => 'application/pdf',
                'file_size' => 1048576, // 1MB
                'metadata' => [
                    'extension' => 'pdf'
                ],
                'is_favorite' => true,
                'usage_count' => 8,
                'last_used_at' => now()->subHours(6),
            ],
            [
                'template_group_id' => $marketingGroup->id,
                'name' => 'Product Launch Carousel',
                'description' => 'Dynamic product launch carousel template',
                'file_name' => 'product-launch-carousel.psd',
                'file_type' => 'image/vnd.adobe.photoshop',
                'file_size' => 2097152, // 2MB
                'metadata' => [
                    'extension' => 'psd'
                ],
                'usage_count' => 2,
                'last_used_at' => now()->subDays(5),
            ],
            [
                'template_group_id' => null, // Ungrouped
                'name' => 'Generic Template',
                'description' => 'A versatile template for various uses',
                'file_name' => 'generic-template.ai',
                'file_type' => 'application/illustrator',
                'file_size' => 1572864, // 1.5MB
                'metadata' => [
                    'extension' => 'ai'
                ],
                'usage_count' => 1,
                'last_used_at' => now()->subWeek(),
            ],
        ];

        foreach ($templates as $templateData) {
            // Create a fake file path (in real usage, files would be uploaded)
            $filePath = "templates/{$adminUser->id}/" . $templateData['file_name'];

            Template::create(array_merge($templateData, [
                'user_id' => $adminUser->id,
                'file_path' => $filePath,
            ]));
        }
    }
}
