<x-dashboard-layout>
    <x-slot name="header">
        Carousel Generator
    </x-slot>

    <div class="space-y-8" x-data="carouselGenerator()">
        <!-- Header Section -->
        <div class="card-elevated p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-2xl font-bold text-primary">Carousel Generator</h2>
                    <p class="mt-1 text-secondary">Generate carousel content from JSON data using your templates.</p>
                </div>
                <div class="flex items-center space-x-4">
                    <button @click="showHistoryModal = true" class="btn-secondary">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Generation History
                    </button>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
            <div class="card-primary p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-secondary">Total Generations</p>
                        <p class="text-2xl font-semibold text-primary">{{ $stats['total_generations'] }}</p>
                    </div>
                </div>
            </div>

            <div class="card-primary p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-secondary">Completed</p>
                        <p class="text-2xl font-semibold text-primary">{{ $stats['completed_generations'] }}</p>
                    </div>
                </div>
            </div>

            <div class="card-primary p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-secondary">Processing</p>
                        <p class="text-2xl font-semibold text-primary">{{ $stats['processing_generations'] }}</p>
                    </div>
                </div>
            </div>

            <div class="card-primary p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-secondary">Available Templates</p>
                        <p class="text-2xl font-semibold text-primary">{{ $stats['available_templates'] }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Generator Interface (Using Tailwind Plus two_column_with_cards template) -->
        <div class="divide-y divide-gray-900/10 dark:divide-gray-700">
            <!-- Step 1: JSON Data Upload -->
            <div class="grid grid-cols-1 gap-x-8 gap-y-8 py-10 md:grid-cols-3">
                <div class="px-4 sm:px-0">
                    <h2 class="text-base/7 font-semibold text-primary">Step 1: Upload JSON Data</h2>
                    <p class="mt-1 text-sm/6 text-secondary">Upload your JSON data file or paste JSON content directly.</p>
                </div>

                <div class="card-primary ring-1 shadow-xs ring-gray-900/5 dark:ring-gray-700 sm:rounded-xl md:col-span-2">
                    <div class="px-4 py-6 sm:p-8">
                        <div class="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8">
                            <!-- JSON File Upload -->
                            <div class="col-span-full">
                                <label for="json-file" class="block text-sm/6 font-medium text-primary">JSON File</label>
                                <div class="mt-2 flex justify-center rounded-lg border border-dashed border-gray-900/25 dark:border-gray-600 px-6 py-10"
                                     @dragover.prevent="jsonDragOver = true"
                                     @dragleave.prevent="jsonDragOver = false"
                                     @drop.prevent="handleJsonFileDrop"
                                     :class="jsonDragOver ? 'border-indigo-500 bg-indigo-50 dark:bg-indigo-900/20' : ''">
                                    <div class="text-center">
                                        <svg class="mx-auto size-12 text-gray-300" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
                                            <path d="M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0016.5 9h-1.875a1.875 1.875 0 01-1.875-1.875V5.25A3.75 3.75 0 009 1.5H5.625z"></path>
                                            <path d="M12.971 1.816A5.23 5.23 0 0114.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 013.434 1.279 9.768 9.768 0 00-6.963-6.963z"></path>
                                        </svg>
                                        <div class="mt-4 flex text-sm/6 text-secondary">
                                            <label for="json-file" class="relative cursor-pointer rounded-md font-semibold text-indigo-600 focus-within:ring-2 focus-within:ring-indigo-600 focus-within:ring-offset-2 focus-within:outline-hidden hover:text-indigo-500">
                                                <span>Upload a JSON file</span>
                                                <input id="json-file" name="json-file" type="file" class="sr-only" @change="handleJsonFileSelect" accept=".json">
                                            </label>
                                            <p class="pl-1">or drag and drop</p>
                                        </div>
                                        <p class="text-xs/5 text-secondary">JSON files only</p>
                                    </div>
                                </div>
                            </div>

                            <!-- JSON Text Area -->
                            <div class="col-span-full">
                                <label for="json-content" class="block text-sm/6 font-medium text-primary">Or paste JSON content</label>
                                <div class="mt-2">
                                    <textarea name="json-content" id="json-content" rows="8" x-model="jsonContent" 
                                              class="input-field font-mono text-sm" 
                                              placeholder='[{"title": "Sample Title", "description": "Sample Description"}]'></textarea>
                                </div>
                                <p class="mt-3 text-sm/6 text-secondary">Paste your JSON data here. Must be an array of objects.</p>
                            </div>

                            <!-- Validate Button -->
                            <div class="col-span-full">
                                <button type="button" @click="validateJson" :disabled="!jsonContent" 
                                        class="btn-primary" :class="!jsonContent ? 'opacity-50 cursor-not-allowed' : ''">
                                    <span x-show="!validatingJson">Validate JSON</span>
                                    <span x-show="validatingJson">Validating...</span>
                                </button>
                            </div>

                            <!-- JSON Validation Results -->
                            <div x-show="jsonValidated" class="col-span-full">
                                <div x-show="jsonValid" class="rounded-md bg-green-50 dark:bg-green-900/20 p-4">
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <h3 class="text-sm font-medium text-green-800 dark:text-green-200">JSON Valid</h3>
                                            <div class="mt-2 text-sm text-green-700 dark:text-green-300">
                                                <p x-text="`Found ${totalItems} items with fields: ${availableFields.join(', ')}`"></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div x-show="!jsonValid" class="rounded-md bg-red-50 dark:bg-red-900/20 p-4">
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <h3 class="text-sm font-medium text-red-800 dark:text-red-200">JSON Invalid</h3>
                                            <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                                                <p x-text="jsonErrors"></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 2: Template Selection -->
            <div x-show="jsonValid" class="grid grid-cols-1 gap-x-8 gap-y-8 py-10 md:grid-cols-3">
                <div class="px-4 sm:px-0">
                    <h2 class="text-base/7 font-semibold text-primary">Step 2: Select Template</h2>
                    <p class="mt-1 text-sm/6 text-secondary">Choose a template to use for generating your carousel content.</p>
                </div>

                <div class="card-primary ring-1 shadow-xs ring-gray-900/5 dark:ring-gray-700 sm:rounded-xl md:col-span-2">
                    <div class="px-4 py-6 sm:p-8">
                        <div class="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8">
                            @if($templates->count() > 0)
                                <div class="col-span-full">
                                    <label class="block text-sm/6 font-medium text-primary mb-4">Available Templates</label>
                                    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                                        @foreach($templates as $template)
                                        <div class="relative cursor-pointer" @click="selectTemplate({{ $template->id }})">
                                            <div class="card-secondary p-4 hover:shadow-md transition-shadow duration-200"
                                                 :class="selectedTemplateId === {{ $template->id }} ? 'ring-2 ring-indigo-500' : ''">
                                                <!-- Template Preview -->
                                                <div class="aspect-w-16 aspect-h-9 mb-3 bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden">
                                                    @if($template->thumbnail_url)
                                                        <img src="{{ $template->thumbnail_url }}" alt="{{ $template->name }}"
                                                             class="w-full h-full object-cover">
                                                    @else
                                                        <div class="w-full h-full flex items-center justify-center">
                                                            <svg class="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                            </svg>
                                                        </div>
                                                    @endif
                                                </div>

                                                <!-- Template Info -->
                                                <div>
                                                    <h4 class="text-sm font-medium text-primary">{{ $template->name }}</h4>
                                                    <p class="text-xs text-secondary mt-1">{{ $template->description ?: 'No description' }}</p>
                                                    <div class="mt-2 flex items-center justify-between text-xs text-secondary">
                                                        <span>{{ $template->formatted_file_size }}</span>
                                                        @if($template->templateGroup)
                                                            <span class="inline-flex items-center px-2 py-1 rounded-full bg-gray-100 dark:bg-gray-700">
                                                                <div class="w-2 h-2 rounded-full mr-1" style="background-color: {{ $template->templateGroup->color }}"></div>
                                                                {{ $template->templateGroup->name }}
                                                            </span>
                                                        @endif
                                                    </div>
                                                </div>

                                                <!-- Selection Indicator -->
                                                <div x-show="selectedTemplateId === {{ $template->id }}"
                                                     class="absolute top-2 right-2 w-6 h-6 bg-indigo-600 rounded-full flex items-center justify-center">
                                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                    </svg>
                                                </div>
                                            </div>
                                        </div>
                                        @endforeach
                                    </div>
                                </div>
                            @else
                                <div class="col-span-full text-center py-8">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    <h3 class="mt-2 text-sm font-medium text-primary">No templates available</h3>
                                    <p class="mt-1 text-sm text-secondary">Upload some image templates first to use the generator.</p>
                                    <div class="mt-6">
                                        <a href="{{ route('templates.index') }}" class="btn-primary">Upload Templates</a>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 3: Field Mapping -->
            <div x-show="selectedTemplateId" class="grid grid-cols-1 gap-x-8 gap-y-8 py-10 md:grid-cols-3">
                <div class="px-4 sm:px-0">
                    <h2 class="text-base/7 font-semibold text-primary">Step 3: Map Fields</h2>
                    <p class="mt-1 text-sm/6 text-secondary">Map your JSON fields to text positions on the template.</p>
                </div>

                <div class="card-primary ring-1 shadow-xs ring-gray-900/5 dark:ring-gray-700 sm:rounded-xl md:col-span-2">
                    <div class="px-4 py-6 sm:p-8">
                        <div class="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8">
                            <!-- Field Mapping Interface -->
                            <div class="col-span-full">
                                <label class="block text-sm/6 font-medium text-primary mb-4">Field Mapping</label>
                                <div class="space-y-4">
                                    <template x-for="(field, index) in availableFields" :key="field">
                                        <div class="card-secondary p-4">
                                            <div class="flex items-center justify-between mb-3">
                                                <h4 class="text-sm font-medium text-primary" x-text="field"></h4>
                                                <label class="relative inline-flex items-center cursor-pointer">
                                                    <input type="checkbox" x-model="fieldMappings[field].enabled" class="sr-only peer">
                                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 dark:peer-focus:ring-indigo-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-indigo-600"></div>
                                                </label>
                                            </div>

                                            <div x-show="fieldMappings[field].enabled" class="grid grid-cols-2 gap-4">
                                                <div>
                                                    <label class="block text-xs font-medium text-secondary mb-1">X Position (%)</label>
                                                    <input type="number" x-model="fieldMappings[field].x" min="0" max="100"
                                                           class="input-field text-sm" placeholder="50">
                                                </div>
                                                <div>
                                                    <label class="block text-xs font-medium text-secondary mb-1">Y Position (%)</label>
                                                    <input type="number" x-model="fieldMappings[field].y" min="0" max="100"
                                                           class="input-field text-sm" placeholder="50">
                                                </div>
                                                <div>
                                                    <label class="block text-xs font-medium text-secondary mb-1">Font Size</label>
                                                    <input type="number" x-model="fieldMappings[field].font_size" min="8" max="72"
                                                           class="input-field text-sm" placeholder="24">
                                                </div>
                                                <div>
                                                    <label class="block text-xs font-medium text-secondary mb-1">Font Color</label>
                                                    <input type="color" x-model="fieldMappings[field].font_color"
                                                           class="w-full h-8 rounded border border-gray-300 dark:border-gray-600">
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                </div>
                            </div>

                            <!-- Generation Configuration -->
                            <div class="col-span-full">
                                <label class="block text-sm/6 font-medium text-primary mb-4">Generation Settings</label>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label for="generation-name" class="block text-sm/6 font-medium text-primary">Generation Name</label>
                                        <div class="mt-2">
                                            <input type="text" id="generation-name" x-model="generationName"
                                                   class="input-field" placeholder="My Carousel Generation">
                                        </div>
                                    </div>
                                    <div>
                                        <label for="default-font-size" class="block text-sm/6 font-medium text-primary">Default Font Size</label>
                                        <div class="mt-2">
                                            <input type="number" id="default-font-size" x-model="generationConfig.default_font_size"
                                                   min="8" max="72" class="input-field" placeholder="24">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Preview and Generate -->
                            <div class="col-span-full">
                                <div class="flex items-center justify-between">
                                    <button type="button" @click="generatePreview" :disabled="!canPreview"
                                            class="btn-secondary" :class="!canPreview ? 'opacity-50 cursor-not-allowed' : ''">
                                        <span x-show="!generatingPreview">Generate Preview</span>
                                        <span x-show="generatingPreview">Generating...</span>
                                    </button>
                                    <button type="button" @click="startGeneration" :disabled="!canGenerate"
                                            class="btn-primary" :class="!canGenerate ? 'opacity-50 cursor-not-allowed' : ''">
                                        <span x-show="!generating">Generate All</span>
                                        <span x-show="generating">Generating...</span>
                                    </button>
                                </div>
                            </div>

                            <!-- Preview Display -->
                            <div x-show="previewUrl" class="col-span-full">
                                <label class="block text-sm/6 font-medium text-primary mb-4">Preview</label>
                                <div class="card-secondary p-4">
                                    <img :src="previewUrl" alt="Preview" class="max-w-full h-auto rounded-lg">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function carouselGenerator() {
            return {
                // State
                jsonContent: '',
                jsonDragOver: false,
                jsonValidated: false,
                jsonValid: false,
                jsonErrors: '',
                validatingJson: false,
                availableFields: [],
                totalItems: 0,
                sampleItem: null,
                sourceData: [],

                // Template Selection
                selectedTemplateId: null,

                // Field Mapping
                fieldMappings: {},

                // Generation
                generationName: '',
                generationConfig: {
                    default_font_size: 24,
                    default_font_color: '#000000'
                },

                // Preview
                previewUrl: '',
                generatingPreview: false,

                // Generation Process
                generating: false,
                showHistoryModal: false,

                init() {
                    // Initialize component
                },

                get canPreview() {
                    return this.jsonValid && this.selectedTemplateId && this.hasEnabledMappings;
                },

                get canGenerate() {
                    return this.canPreview && this.generationName.trim();
                },

                get hasEnabledMappings() {
                    return Object.values(this.fieldMappings).some(mapping => mapping.enabled);
                },

                handleJsonFileSelect(event) {
                    const file = event.target.files[0];
                    if (file && file.type === 'application/json') {
                        const reader = new FileReader();
                        reader.onload = (e) => {
                            this.jsonContent = e.target.result;
                        };
                        reader.readAsText(file);
                    }
                },

                handleJsonFileDrop(event) {
                    this.jsonDragOver = false;
                    const files = event.dataTransfer.files;
                    if (files.length > 0 && files[0].type === 'application/json') {
                        const reader = new FileReader();
                        reader.onload = (e) => {
                            this.jsonContent = e.target.result;
                        };
                        reader.readAsText(files[0]);
                    }
                },

                async validateJson() {
                    if (!this.jsonContent.trim()) return;

                    this.validatingJson = true;
                    this.jsonValidated = false;

                    try {
                        const response = await fetch('/generator/validate-json', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            },
                            body: JSON.stringify({
                                json_data: this.jsonContent
                            })
                        });

                        const result = await response.json();

                        if (result.success) {
                            this.jsonValid = true;
                            this.availableFields = result.available_fields;
                            this.totalItems = result.total_items;
                            this.sampleItem = result.sample_item;
                            this.sourceData = result.data;
                            this.initializeFieldMappings();
                        } else {
                            this.jsonValid = false;
                            this.jsonErrors = result.errors ? result.errors.join(', ') : result.message;
                        }

                        this.jsonValidated = true;
                    } catch (error) {
                        this.jsonValid = false;
                        this.jsonErrors = 'Failed to validate JSON: ' + error.message;
                        this.jsonValidated = true;
                    } finally {
                        this.validatingJson = false;
                    }
                },

                initializeFieldMappings() {
                    this.fieldMappings = {};
                    this.availableFields.forEach((field, index) => {
                        this.fieldMappings[field] = {
                            enabled: index < 3, // Enable first 3 fields by default
                            x: 10 + (index * 20), // Spread them out
                            y: 20 + (index * 15),
                            font_size: 24,
                            font_color: '#000000'
                        };
                    });
                },

                selectTemplate(templateId) {
                    this.selectedTemplateId = templateId;
                    this.previewUrl = ''; // Clear previous preview
                },

                async generatePreview() {
                    if (!this.canPreview) return;

                    this.generatingPreview = true;

                    try {
                        const response = await fetch('/generator/preview', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            },
                            body: JSON.stringify({
                                template_id: this.selectedTemplateId,
                                item_data: this.sampleItem,
                                mapped_data: this.fieldMappings,
                                generation_config: this.generationConfig
                            })
                        });

                        const result = await response.json();

                        if (result.success) {
                            this.previewUrl = result.preview_url;
                        } else {
                            console.error('Preview generation failed:', result.message);
                        }
                    } catch (error) {
                        console.error('Preview generation error:', error);
                    } finally {
                        this.generatingPreview = false;
                    }
                },

                async startGeneration() {
                    if (!this.canGenerate) return;

                    this.generating = true;

                    try {
                        const response = await fetch('/generator/generate', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            },
                            body: JSON.stringify({
                                name: this.generationName,
                                template_id: this.selectedTemplateId,
                                source_data: this.sourceData,
                                mapped_data: this.fieldMappings,
                                generation_config: this.generationConfig
                            })
                        });

                        const result = await response.json();

                        if (result.success) {
                            // Show success message and redirect or refresh
                            console.log('Generation started successfully:', result.generation_id);
                            // You could redirect to a status page or show a modal
                        } else {
                            console.error('Generation failed:', result.message);
                        }
                    } catch (error) {
                        console.error('Generation error:', error);
                    } finally {
                        this.generating = false;
                    }
                }
            }
        }
    </script>
</x-dashboard-layout>
