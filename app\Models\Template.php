<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

class Template extends Model
{
    protected $fillable = [
        'user_id',
        'template_group_id',
        'name',
        'description',
        'file_path',
        'file_name',
        'file_type',
        'file_size',
        'thumbnail_path',
        'metadata',
        'sort_order',
        'is_favorite',
        'usage_count',
        'last_used_at',
    ];

    protected $casts = [
        'metadata' => 'array',
        'is_favorite' => 'boolean',
        'last_used_at' => 'datetime',
    ];

    /**
     * Get the user that owns the template.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the template group this template belongs to.
     */
    public function templateGroup(): BelongsTo
    {
        return $this->belongsTo(TemplateGroup::class);
    }

    /**
     * Get the full URL for the template file.
     */
    public function getFileUrlAttribute(): string
    {
        return Storage::url($this->file_path);
    }

    /**
     * Get the full URL for the thumbnail.
     */
    public function getThumbnailUrlAttribute(): ?string
    {
        return $this->thumbnail_path ? Storage::url($this->thumbnail_path) : null;
    }

    /**
     * Get formatted file size.
     */
    public function getFormattedFileSizeAttribute(): string
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Check if the template is an image.
     */
    public function getIsImageAttribute(): bool
    {
        return str_starts_with($this->file_type, 'image/');
    }

    /**
     * Increment usage count and update last used timestamp.
     */
    public function incrementUsage(): void
    {
        $this->increment('usage_count');
        $this->update(['last_used_at' => now()]);
    }

    /**
     * Scope to get templates for a specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to get favorite templates.
     */
    public function scopeFavorites($query)
    {
        return $query->where('is_favorite', true);
    }

    /**
     * Scope to get templates by group.
     */
    public function scopeInGroup($query, $groupId)
    {
        return $query->where('template_group_id', $groupId);
    }

    /**
     * Scope to get ungrouped templates.
     */
    public function scopeUngrouped($query)
    {
        return $query->whereNull('template_group_id');
    }

    /**
     * Scope to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Scope to order by most recently used.
     */
    public function scopeRecentlyUsed($query)
    {
        return $query->orderByDesc('last_used_at');
    }

    /**
     * Scope to order by most used.
     */
    public function scopeMostUsed($query)
    {
        return $query->orderByDesc('usage_count');
    }
}
