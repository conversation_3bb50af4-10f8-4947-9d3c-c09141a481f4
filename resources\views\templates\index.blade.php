<x-dashboard-layout>
    <x-slot name="header">
        Template Manager
    </x-slot>

    <style>
        .sortable-ghost {
            opacity: 0.4;
        }
        .sortable-chosen {
            transform: scale(1.05);
        }
        .sortable-drag {
            transform: rotate(5deg);
        }
        .sortable-enabled .card-secondary {
            cursor: move;
        }

        /* Instagram-style Carousel Styles */
        .carousel-container {
            position: relative;
            overflow: hidden;
            border-radius: 0.5rem;
        }

        .carousel-track {
            display: flex;
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            will-change: transform;
        }

        .carousel-slide {
            flex: 0 0 100%;
            width: 100%;
            height: 100%;
        }

        .carousel-nav-btn {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            width: 44px;
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            z-index: 20;
            backdrop-filter: blur(4px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .carousel-nav-btn:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-50%) scale(1.1);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        }

        .carousel-nav-btn:active {
            transform: translateY(-50%) scale(0.95);
        }

        .carousel-nav-btn.prev {
            left: 12px;
        }

        .carousel-nav-btn.next {
            right: 12px;
        }

        .carousel-dots {
            position: absolute;
            bottom: 16px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 8px;
            background: rgba(0, 0, 0, 0.6);
            padding: 8px 16px;
            border-radius: 20px;
            backdrop-filter: blur(8px);
            z-index: 20;
        }

        .carousel-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .carousel-dot:hover {
            background: rgba(255, 255, 255, 0.8);
            transform: scale(1.2);
        }

        .carousel-dot.active {
            background: white;
            transform: scale(1.3);
            box-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
        }

        /* Touch/Swipe Support */
        .carousel-container.dragging .carousel-track {
            transition: none;
        }

        /* Responsive adjustments */
        @media (max-width: 640px) {
            .carousel-nav-btn {
                width: 36px;
                height: 36px;
            }

            .carousel-nav-btn.prev {
                left: 8px;
            }

            .carousel-nav-btn.next {
                right: 8px;
            }

            .carousel-dots {
                bottom: 12px;
                padding: 6px 12px;
            }

            .carousel-dot {
                width: 6px;
                height: 6px;
            }
        }

        @media (max-width: 375px) {
            .carousel-nav-btn {
                width: 32px;
                height: 32px;
            }

            .carousel-nav-btn svg {
                width: 16px;
                height: 16px;
            }
        }
    </style>

    <div class="space-y-8" x-data="templateManager()">
        <!-- Header Section -->
        <div class="card-elevated p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-2xl font-bold text-primary">Template Manager</h2>
                    <p class="mt-1 text-secondary">Upload, organize, and manage your carousel templates.</p>
                </div>
                <div class="flex items-center space-x-4">
                    <button @click="showUploadModal = true" class="btn-primary">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Upload Template
                    </button>
                    <button @click="showMultiUploadModal = true" class="btn-primary">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        Create Group with Files
                    </button>
                    <button @click="showGroupModal = true" class="btn-secondary">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                        New Group
                    </button>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
            <div class="card-primary p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-secondary">Total Templates</p>
                        <p class="text-2xl font-semibold text-primary">{{ $stats['total_templates'] }}</p>
                    </div>
                </div>
            </div>

            <div class="card-primary p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-secondary">Groups</p>
                        <p class="text-2xl font-semibold text-primary">{{ $stats['total_groups'] }}</p>
                    </div>
                </div>
            </div>

            <div class="card-primary p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-secondary">Favorites</p>
                        <p class="text-2xl font-semibold text-primary">{{ $stats['favorite_templates'] }}</p>
                    </div>
                </div>
            </div>

            <div class="card-primary p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-secondary">Storage Used</p>
                        <p class="text-2xl font-semibold text-primary">{{ $stats['storage_used'] }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Template Groups and Templates -->
        <div class="grid grid-cols-1 gap-8 lg:grid-cols-4">
            <!-- Sidebar with Groups -->
            <div class="lg:col-span-1">
                <div class="card-elevated p-6">
                    <h3 class="text-lg font-semibold text-primary mb-4">Template Groups</h3>
                    
                    <!-- All Templates -->
                    <div class="space-y-2">
                        <button @click="selectedGroup = null" 
                                :class="selectedGroup === null ? 'bg-indigo-50 dark:bg-indigo-900 text-indigo-700 dark:text-indigo-300' : 'text-secondary hover:bg-gray-50 dark:hover:bg-gray-700'"
                                class="w-full text-left px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                            <div class="flex items-center justify-between">
                                <span>All Templates</span>
                                <span class="text-xs bg-gray-200 dark:bg-gray-600 px-2 py-1 rounded-full">{{ $stats['total_templates'] }}</span>
                            </div>
                        </button>
                        
                        <button @click="selectedGroup = 'favorites'" 
                                :class="selectedGroup === 'favorites' ? 'bg-indigo-50 dark:bg-indigo-900 text-indigo-700 dark:text-indigo-300' : 'text-secondary hover:bg-gray-50 dark:hover:bg-gray-700'"
                                class="w-full text-left px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                            <div class="flex items-center justify-between">
                                <span>Favorites</span>
                                <span class="text-xs bg-gray-200 dark:bg-gray-600 px-2 py-1 rounded-full">{{ $stats['favorite_templates'] }}</span>
                            </div>
                        </button>
                        
                        <button @click="selectedGroup = 'ungrouped'" 
                                :class="selectedGroup === 'ungrouped' ? 'bg-indigo-50 dark:bg-indigo-900 text-indigo-700 dark:text-indigo-300' : 'text-secondary hover:bg-gray-50 dark:hover:bg-gray-700'"
                                class="w-full text-left px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                            <div class="flex items-center justify-between">
                                <span>Ungrouped</span>
                                <span class="text-xs bg-gray-200 dark:bg-gray-600 px-2 py-1 rounded-full">{{ $ungroupedTemplates->count() }}</span>
                            </div>
                        </button>
                    </div>

                    <!-- Custom Groups -->
                    @if($templateGroups->count() > 0)
                        <div class="mt-6">
                            <h4 class="text-sm font-medium text-secondary mb-3">Custom Groups</h4>
                            <div class="space-y-2">
                                @foreach($templateGroups as $group)
                                <button @click="selectedGroup = {{ $group->id }}" 
                                        :class="selectedGroup === {{ $group->id }} ? 'bg-indigo-50 dark:bg-indigo-900 text-indigo-700 dark:text-indigo-300' : 'text-secondary hover:bg-gray-50 dark:hover:bg-gray-700'"
                                        class="w-full text-left px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="w-3 h-3 rounded-full mr-2" style="background-color: {{ $group->color }}"></div>
                                            <span>{{ $group->name }}</span>
                                        </div>
                                        <span class="text-xs bg-gray-200 dark:bg-gray-600 px-2 py-1 rounded-full">{{ $group->templates_count }}</span>
                                    </div>
                                </button>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Main Content Area -->
            <div class="lg:col-span-3">
                <div class="card-elevated p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-primary" x-text="getGroupTitle()"></h3>
                        <div class="flex items-center space-x-2">
                            <button @click="viewMode = 'grid'" 
                                    :class="viewMode === 'grid' ? 'bg-indigo-100 dark:bg-indigo-900 text-indigo-700 dark:text-indigo-300' : 'text-secondary'"
                                    class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                                </svg>
                            </button>
                            <button @click="viewMode = 'list'" 
                                    :class="viewMode === 'list' ? 'bg-indigo-100 dark:bg-indigo-900 text-indigo-700 dark:text-indigo-300' : 'text-secondary'"
                                    class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- Templates Grid/List -->
                    <div x-show="viewMode === 'grid'" class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3"
                         x-ref="templatesGrid" x-init="initSortable()"
                         :class="selectedGroup && selectedGroup !== 'favorites' && selectedGroup !== 'ungrouped' && selectedGroup !== null ? 'sortable-enabled' : ''"
                         data-group-id="selectedGroup">
                        <template x-for="(item, index) in filteredTemplates" :key="item.id">
                            <div class="card-secondary p-4 hover:shadow-lg transition-shadow duration-200" :data-index="index">

                                <!-- Template Group Carousel -->
                                <div x-show="item.isGroup" x-data="templateCarousel(item.templates)">
                                    <!-- Instagram-style Carousel Preview -->
                                    <div class="aspect-w-16 aspect-h-9 mb-4 bg-gray-100 dark:bg-gray-700 carousel-container"
                                         x-ref="carouselContainer"
                                         @touchstart="handleTouchStart($event)"
                                         @touchmove="handleTouchMove($event)"
                                         @touchend="handleTouchEnd($event)"
                                         @mousedown="handleMouseDown($event)"
                                         @mousemove="handleMouseMove($event)"
                                         @mouseup="handleMouseUp($event)"
                                         @mouseleave="handleMouseUp($event)">

                                        <!-- Carousel Track -->
                                        <div class="carousel-track h-full"
                                             x-ref="carouselTrack"
                                             :style="`transform: translateX(-${currentIndex * 100}%)`">
                                            <template x-for="(template, templateIndex) in item.templates" :key="template.id">
                                                <div class="carousel-slide">
                                                    <div x-show="template.is_image" class="w-full h-full">
                                                        <img :src="template.thumbnail_url || template.file_url"
                                                             :alt="template.name"
                                                             class="w-full h-full object-cover select-none"
                                                             draggable="false">
                                                    </div>
                                                    <div x-show="!template.is_image"
                                                         class="w-full h-full flex items-center justify-center">
                                                        <div class="text-center">
                                                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                            </svg>
                                                            <p class="mt-2 text-sm text-secondary" x-text="template.file_type"></p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </template>
                                        </div>

                                        <!-- Navigation Arrows -->
                                        <button x-show="item.templates && item.templates.length > 1"
                                                @click="previousSlide()"
                                                aria-label="Previous image"
                                                class="carousel-nav-btn prev">
                                            <svg class="w-5 h-5 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M15 19l-7-7 7-7"></path>
                                            </svg>
                                        </button>
                                        <button x-show="item.templates && item.templates.length > 1"
                                                @click="nextSlide()"
                                                aria-label="Next image"
                                                class="carousel-nav-btn next">
                                            <svg class="w-5 h-5 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M9 5l7 7-7 7"></path>
                                            </svg>
                                        </button>

                                        <!-- Dots Indicator -->
                                        <div x-show="item.templates && item.templates.length > 1" class="carousel-dots">
                                            <template x-for="(template, dotIndex) in item.templates" :key="dotIndex">
                                                <button @click="goToSlide(dotIndex)"
                                                        :aria-label="'Go to slide ' + (dotIndex + 1)"
                                                        :class="currentIndex === dotIndex ? 'carousel-dot active' : 'carousel-dot'"
                                                        class="carousel-dot"></button>
                                            </template>
                                        </div>
                                    </div>

                                    <!-- Group Info -->
                                    <div class="flex-1">
                                        <div class="flex items-start justify-between">
                                            <div class="flex-1 min-w-0">
                                                <h4 class="text-sm font-medium text-primary truncate" x-text="item.name"></h4>
                                                <p class="text-xs text-secondary mt-1" x-text="item.description || 'No description'"></p>
                                                <p class="text-xs text-secondary mt-1" x-text="(item.templates ? item.templates.length : 0) + ' templates'"></p>
                                            </div>
                                        </div>

                                        <!-- Group Badge -->
                                        <div class="mt-2">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
                                                <div class="w-2 h-2 rounded-full mr-1" :style="`background-color: ${item.color}`"></div>
                                                <span x-text="item.name"></span>
                                            </span>
                                        </div>
                                    </div>

                                    <!-- Group Actions -->
                                    <div class="mt-4 flex items-center justify-between">
                                        <div class="flex items-center space-x-2">
                                            <button @click="downloadCurrentTemplate()" class="btn-sm btn-secondary">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                </svg>
                                                Download
                                            </button>
                                            <button @click="editCurrentTemplate()" class="btn-sm btn-secondary">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                </svg>
                                                Edit
                                            </button>
                                        </div>
                                        <button @click="deleteCurrentTemplate()" class="text-red-600 hover:text-red-500">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>

                                <!-- Individual Template -->
                                <div x-show="!item.isGroup">
                                    <!-- Template Preview -->
                                    <div class="aspect-w-16 aspect-h-9 mb-4 bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden">
                                        <div x-show="item.is_image" class="w-full h-full">
                                            <img :src="item.thumbnail_url || item.file_url" :alt="item.name"
                                                 class="w-full h-full object-cover">
                                        </div>
                                        <div x-show="!item.is_image"
                                             class="w-full h-full flex items-center justify-center">
                                            <div class="text-center">
                                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                </svg>
                                                <p class="mt-2 text-sm text-secondary" x-text="item.file_type"></p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Template Info -->
                                    <div class="flex-1">
                                        <div class="flex items-start justify-between">
                                            <div class="flex-1 min-w-0">
                                                <h4 class="text-sm font-medium text-primary truncate" x-text="item.name"></h4>
                                                <p class="text-xs text-secondary mt-1" x-text="item.description || 'No description'"></p>
                                            </div>
                                            <button @click="toggleFavorite(item)"
                                                    :class="item.is_favorite ? 'text-red-500' : 'text-gray-400'"
                                                    class="ml-2 hover:text-red-500 transition-colors duration-200">
                                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                                </svg>
                                            </button>
                                        </div>

                                        <!-- Template Meta -->
                                        <div class="mt-3 flex items-center justify-between text-xs text-secondary">
                                            <span x-text="item.formatted_file_size"></span>
                                            <span x-text="item.usage_count + ' uses'"></span>
                                        </div>

                                        <!-- Template Group -->
                                        <div x-show="item.template_group" class="mt-2">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
                                                <div class="w-2 h-2 rounded-full mr-1" :style="`background-color: ${item.template_group?.color || '#6366f1'}`"></div>
                                                <span x-text="item.template_group?.name || 'Ungrouped'"></span>
                                            </span>
                                        </div>
                                    </div>

                                    <!-- Actions -->
                                    <div class="mt-4 flex items-center justify-between">
                                        <div class="flex items-center space-x-2">
                                            <button @click="downloadTemplate(item)" class="btn-sm btn-secondary">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                </svg>
                                                Download
                                            </button>
                                            <button @click="editTemplate(item)" class="btn-sm btn-secondary">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                </svg>
                                                Edit
                                            </button>
                                        </div>
                                        <button @click="deleteTemplate(item)" class="text-red-600 hover:text-red-500">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>

                    <div x-show="viewMode === 'list'" class="space-y-4">
                        <template x-for="template in filteredTemplates" :key="template.id">
                            <div class="card-secondary p-4 hover:shadow-md transition-shadow duration-200">
                                <div class="flex items-center space-x-4">
                                    <!-- Template Preview -->
                                    <div class="flex-shrink-0 w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden">
                                        <div x-show="template.is_image" class="w-full h-full">
                                            <img :src="template.thumbnail_url || template.file_url" :alt="template.name"
                                                 class="w-full h-full object-cover">
                                        </div>
                                        <div x-show="!template.is_image"
                                             class="w-full h-full flex items-center justify-center">
                                            <svg class="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                            </svg>
                                        </div>
                                    </div>

                                    <!-- Template Info -->
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-center justify-between">
                                            <h4 class="text-sm font-medium text-primary truncate" x-text="template.name"></h4>
                                            <button @click="toggleFavorite(template)"
                                                    :class="template.is_favorite ? 'text-red-500' : 'text-gray-400'"
                                                    class="ml-2 hover:text-red-500 transition-colors duration-200">
                                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                                </svg>
                                            </button>
                                        </div>
                                        <p class="text-xs text-secondary mt-1" x-text="template.description || 'No description'"></p>
                                        <div class="mt-2 flex items-center space-x-4 text-xs text-secondary">
                                            <span x-text="template.formatted_file_size"></span>
                                            <span x-text="template.usage_count + ' uses'"></span>
                                            <span x-show="template.template_group" class="inline-flex items-center">
                                                <div class="w-2 h-2 rounded-full mr-1" :style="`background-color: ${template.template_group?.color || '#6366f1'}`"></div>
                                                <span x-text="template.template_group?.name || 'Ungrouped'"></span>
                                            </span>
                                        </div>
                                    </div>

                                    <!-- Actions -->
                                    <div class="flex items-center space-x-2">
                                        <button @click="downloadTemplate(template)" class="btn-sm btn-secondary">Download</button>
                                        <button @click="editTemplate(template)" class="btn-sm btn-secondary">Edit</button>
                                        <button @click="deleteTemplate(template)" class="text-red-600 hover:text-red-500">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>

                    <!-- Empty State -->
                    <div x-show="templates.length === 0" class="text-center py-12">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-primary">No templates found</h3>
                        <p class="mt-1 text-sm text-secondary">Get started by uploading your first template.</p>
                        <div class="mt-6">
                            <button @click="showUploadModal = true" class="btn-primary">Upload Template</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Upload Modal -->
        <div x-show="showUploadModal" class="fixed inset-0 z-50 overflow-y-auto" x-transition>
            <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
                <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" @click="showUploadModal = false"></div>

                <div class="inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform card-primary shadow-xl">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-primary">Upload Template</h3>
                        <button @click="showUploadModal = false" class="text-secondary hover:text-primary">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <form @submit.prevent="uploadTemplate">
                        <!-- File Upload Area (from Tailwind Plus template) -->
                        <div class="col-span-full mb-4">
                            <label for="template-file" class="block text-sm/6 font-medium text-primary">Template File</label>
                            <div class="mt-2 flex justify-center rounded-lg border border-dashed border-gray-900/25 dark:border-gray-600 px-6 py-10"
                                 @dragover.prevent="dragOver = true"
                                 @dragleave.prevent="dragOver = false"
                                 @drop.prevent="handleFileDrop"
                                 :class="dragOver ? 'border-indigo-500 bg-indigo-50 dark:bg-indigo-900/20' : ''">
                                <div class="text-center">
                                    <svg class="mx-auto size-12 text-gray-300" viewBox="0 0 24 24" fill="currentColor">
                                        <path fill-rule="evenodd" d="M1.5 6a2.25 2.25 0 0 1 2.25-2.25h16.5A2.25 2.25 0 0 1 22.5 6v12a2.25 2.25 0 0 1-2.25 2.25H3.75A2.25 2.25 0 0 1 1.5 18V6ZM3 16.06V18c0 .414.336.75.75.75h16.5A.75.75 0 0 0 21 18v-1.94l-2.69-2.689a1.5 1.5 0 0 0-2.12 0l-.88.879.97.97a.75.75 0 1 1-1.06 1.06l-5.16-5.159a1.5 1.5 0 0 0-2.12 0L3 16.061Zm10.125-7.81a1.125 1.125 0 1 1 2.25 0 1.125 1.125 0 0 1-2.25 0Z" clip-rule="evenodd" />
                                    </svg>
                                    <div class="mt-4 flex text-sm/6 text-secondary">
                                        <label for="template-file" class="relative cursor-pointer rounded-md font-semibold text-indigo-600 focus-within:ring-2 focus-within:ring-indigo-600 focus-within:ring-offset-2 focus-within:outline-hidden hover:text-indigo-500">
                                            <span>Upload a file</span>
                                            <input id="template-file" name="template-file" type="file" class="sr-only" @change="handleFileSelect" accept="image/*,.pdf,.ai,.psd">
                                        </label>
                                        <p class="pl-1">or drag and drop</p>
                                    </div>
                                    <p class="text-xs/5 text-secondary">PNG, JPG, GIF, PDF, AI, PSD up to 10MB</p>
                                </div>
                            </div>
                        </div>

                        <!-- Template Name -->
                        <div class="mb-4">
                            <label for="template-name" class="block text-sm font-medium text-primary mb-2">Template Name</label>
                            <input type="text" id="template-name" x-model="uploadForm.name"
                                   class="input-field" placeholder="Enter template name">
                        </div>

                        <!-- Description -->
                        <div class="mb-4">
                            <label for="template-description" class="block text-sm font-medium text-primary mb-2">Description (Optional)</label>
                            <textarea id="template-description" x-model="uploadForm.description" rows="3"
                                      class="input-field" placeholder="Enter template description"></textarea>
                        </div>

                        <!-- Group Selection -->
                        <div class="mb-6">
                            <label for="template-group" class="block text-sm font-medium text-primary mb-2">Group (Optional)</label>
                            <select id="template-group" x-model="uploadForm.template_group_id" class="input-field">
                                <option value="">No Group</option>
                                @foreach($templateGroups as $group)
                                <option value="{{ $group->id }}">{{ $group->name }}</option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Upload Progress -->
                        <div x-show="uploading" class="mb-4">
                            <div class="bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div class="bg-indigo-600 h-2 rounded-full transition-all duration-300" :style="`width: ${uploadProgress}%`"></div>
                            </div>
                            <p class="text-sm text-secondary mt-1" x-text="`Uploading... ${uploadProgress}%`"></p>
                        </div>

                        <!-- Actions -->
                        <div class="flex justify-end space-x-3">
                            <button type="button" @click="showUploadModal = false" class="btn-secondary">Cancel</button>
                            <button type="submit" :disabled="!uploadForm.file || uploading" class="btn-primary">
                                <span x-show="!uploading">Upload</span>
                                <span x-show="uploading">Uploading...</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Group Modal -->
        <div x-show="showGroupModal" class="fixed inset-0 z-50 overflow-y-auto" x-transition>
            <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
                <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" @click="showGroupModal = false"></div>

                <div class="inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform card-primary shadow-xl">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-primary">Create Template Group</h3>
                        <button @click="showGroupModal = false" class="text-secondary hover:text-primary">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <form @submit.prevent="createGroup">
                        <!-- Group Name -->
                        <div class="mb-4">
                            <label for="group-name" class="block text-sm font-medium text-primary mb-2">Group Name</label>
                            <input type="text" id="group-name" x-model="groupForm.name"
                                   class="input-field" placeholder="Enter group name" required>
                        </div>

                        <!-- Description -->
                        <div class="mb-4">
                            <label for="group-description" class="block text-sm font-medium text-primary mb-2">Description (Optional)</label>
                            <textarea id="group-description" x-model="groupForm.description" rows="3"
                                      class="input-field" placeholder="Enter group description"></textarea>
                        </div>

                        <!-- Color -->
                        <div class="mb-6">
                            <label for="group-color" class="block text-sm font-medium text-primary mb-2">Color</label>
                            <div class="flex items-center space-x-3">
                                <input type="color" id="group-color" x-model="groupForm.color"
                                       class="w-12 h-10 rounded border border-gray-300 dark:border-gray-600">
                                <input type="text" x-model="groupForm.color"
                                       class="input-field flex-1" placeholder="#6366f1">
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="flex justify-end space-x-3">
                            <button type="button" @click="showGroupModal = false" class="btn-secondary">Cancel</button>
                            <button type="submit" :disabled="!groupForm.name" class="btn-primary">Create Group</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Multi-File Upload Modal -->
        <div x-show="showMultiUploadModal" class="fixed inset-0 z-50 overflow-y-auto" x-transition>
            <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
                <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" @click="showMultiUploadModal = false"></div>

                <div class="inline-block w-full max-w-2xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform card-primary shadow-xl">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-primary">Create Template Group with Files</h3>
                        <button @click="showMultiUploadModal = false" class="text-secondary hover:text-primary">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <form @submit.prevent="createGroupWithFiles">
                        <!-- Group Details -->
                        <div class="grid grid-cols-1 gap-4 mb-6">
                            <div>
                                <label for="multi-group-name" class="block text-sm font-medium text-primary mb-2">Group Name</label>
                                <input type="text" id="multi-group-name" x-model="multiUploadForm.name"
                                       class="input-field" placeholder="Enter group name" required>
                            </div>
                            <div>
                                <label for="multi-group-description" class="block text-sm font-medium text-primary mb-2">Description (Optional)</label>
                                <textarea id="multi-group-description" x-model="multiUploadForm.description" rows="2"
                                          class="input-field" placeholder="Enter group description"></textarea>
                            </div>
                            <div>
                                <label for="multi-group-color" class="block text-sm font-medium text-primary mb-2">Color</label>
                                <div class="flex items-center space-x-3">
                                    <input type="color" id="multi-group-color" x-model="multiUploadForm.color"
                                           class="w-12 h-10 rounded border border-gray-300 dark:border-gray-600">
                                    <input type="text" x-model="multiUploadForm.color"
                                           class="input-field flex-1" placeholder="#6366f1">
                                </div>
                            </div>
                        </div>

                        <!-- Multi-File Upload Area -->
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-primary mb-2">Template Files (1-20 files)</label>
                            <div class="mt-2 flex justify-center rounded-lg border border-dashed border-gray-900/25 dark:border-gray-600 px-6 py-10"
                                 @dragover.prevent="multiDragOver = true"
                                 @dragleave.prevent="multiDragOver = false"
                                 @drop.prevent="handleMultiFileDrop"
                                 :class="multiDragOver ? 'border-indigo-500 bg-indigo-50 dark:bg-indigo-900/20' : ''">
                                <div class="text-center">
                                    <svg class="mx-auto size-12 text-gray-300" viewBox="0 0 24 24" fill="currentColor">
                                        <path fill-rule="evenodd" d="M1.5 6a2.25 2.25 0 0 1 2.25-2.25h16.5A2.25 2.25 0 0 1 22.5 6v12a2.25 2.25 0 0 1-2.25 2.25H3.75A2.25 2.25 0 0 1 1.5 18V6ZM3 16.06V18c0 .414.336.75.75.75h16.5A.75.75 0 0 0 21 18v-1.94l-2.69-2.689a1.5 1.5 0 0 0-2.12 0l-.88.879.97.97a.75.75 0 1 1-1.06 1.06l-5.16-5.159a1.5 1.5 0 0 0-2.12 0L3 16.061Zm10.125-7.81a1.125 1.125 0 1 1 2.25 0 1.125 1.125 0 0 1-2.25 0Z" clip-rule="evenodd" />
                                    </svg>
                                    <div class="mt-4 flex text-sm/6 text-secondary">
                                        <label for="multi-template-files" class="relative cursor-pointer rounded-md font-semibold text-indigo-600 focus-within:ring-2 focus-within:ring-indigo-600 focus-within:ring-offset-2 focus-within:outline-hidden hover:text-indigo-500">
                                            <span>Upload files</span>
                                            <input id="multi-template-files" name="multi-template-files" type="file" class="sr-only"
                                                   @change="handleMultiFileSelect" accept="image/*,.pdf,.ai,.psd" multiple>
                                        </label>
                                        <p class="pl-1">or drag and drop</p>
                                    </div>
                                    <p class="text-xs/5 text-secondary">PNG, JPG, GIF, PDF, AI, PSD up to 10MB each</p>
                                </div>
                            </div>
                        </div>

                        <!-- File Preview -->
                        <div x-show="multiUploadForm.files.length > 0" class="mb-6">
                            <h4 class="text-sm font-medium text-primary mb-3">Selected Files (<span x-text="multiUploadForm.files.length"></span>)</h4>
                            <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 max-h-60 overflow-y-auto">
                                <template x-for="(file, index) in multiUploadForm.files" :key="index">
                                    <div class="relative group">
                                        <div class="aspect-w-1 aspect-h-1 bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden">
                                            <div x-show="file.preview" class="w-full h-full">
                                                <img :src="file.preview" :alt="file.name" class="w-full h-full object-cover">
                                            </div>
                                            <div x-show="!file.preview" class="w-full h-full flex items-center justify-center">
                                                <svg class="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                </svg>
                                            </div>
                                        </div>
                                        <button @click="removeFile(index)"
                                                class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600 opacity-0 group-hover:opacity-100 transition-opacity">
                                            ×
                                        </button>
                                        <p class="mt-1 text-xs text-secondary truncate" x-text="file.name"></p>
                                    </div>
                                </template>
                            </div>
                        </div>

                        <!-- Upload Progress -->
                        <div x-show="multiUploading" class="mb-4">
                            <div class="bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div class="bg-indigo-600 h-2 rounded-full transition-all duration-300" :style="`width: ${multiUploadProgress}%`"></div>
                            </div>
                            <p class="text-sm text-secondary mt-1" x-text="`Uploading... ${multiUploadProgress}%`"></p>
                        </div>

                        <!-- Actions -->
                        <div class="flex justify-end space-x-3">
                            <button type="button" @click="showMultiUploadModal = false" class="btn-secondary">Cancel</button>
                            <button type="submit" :disabled="multiUploadForm.files.length === 0 || !multiUploadForm.name || multiUploading" class="btn-primary">
                                <span x-show="!multiUploading">Create Group</span>
                                <span x-show="multiUploading">Creating...</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        function templateManager() {
            return {
                // State
                selectedGroup: null,
                viewMode: 'grid',
                templates: @json($ungroupedTemplates->concat($templateGroups->flatMap->templates)->values()),
                allTemplates: @json($ungroupedTemplates->concat($templateGroups->flatMap->templates)->values()),
                showUploadModal: false,
                showGroupModal: false,
                showMultiUploadModal: false,
                uploading: false,
                uploadProgress: 0,
                dragOver: false,
                multiUploading: false,
                multiUploadProgress: 0,
                multiDragOver: false,

                // Forms
                uploadForm: {
                    file: null,
                    name: '',
                    description: '',
                    template_group_id: ''
                },
                groupForm: {
                    name: '',
                    description: '',
                    color: '#6366f1'
                },
                multiUploadForm: {
                    name: '',
                    description: '',
                    color: '#6366f1',
                    files: []
                },

                init() {
                    this.loadTemplates();
                },

                initSortable() {
                    // Initialize sortable only for specific groups (not for All, Favorites, or Ungrouped)
                    this.$nextTick(() => {
                        if (this.$refs.templatesGrid && typeof Sortable !== 'undefined') {
                            new Sortable(this.$refs.templatesGrid, {
                                animation: 150,
                                ghostClass: 'sortable-ghost',
                                chosenClass: 'sortable-chosen',
                                dragClass: 'sortable-drag',
                                disabled: this.selectedGroup === null || this.selectedGroup === 'favorites' || this.selectedGroup === 'ungrouped',
                                onEnd: (evt) => {
                                    this.reorderTemplates(evt);
                                }
                            });
                        }
                    });
                },

                get filteredTemplates() {
                    let templates;
                    if (this.selectedGroup === null) {
                        templates = this.allTemplates;
                    } else if (this.selectedGroup === 'favorites') {
                        templates = this.allTemplates.filter(t => t.is_favorite);
                    } else if (this.selectedGroup === 'ungrouped') {
                        templates = this.allTemplates.filter(t => !t.template_group_id);
                    } else {
                        templates = this.allTemplates.filter(t => t.template_group_id === this.selectedGroup);
                    }

                    // For "All Templates" view, group templates by template_group_id
                    if (this.selectedGroup === null) {
                        return this.groupTemplatesForDisplay(templates);
                    }

                    return templates;
                },

                groupTemplatesForDisplay(templates) {
                    const grouped = {};
                    const ungrouped = [];

                    templates.forEach(template => {
                        if (template.template_group_id) {
                            if (!grouped[template.template_group_id]) {
                                grouped[template.template_group_id] = [];
                            }
                            grouped[template.template_group_id].push(template);
                        } else {
                            ungrouped.push(template);
                        }
                    });

                    const result = [];

                    // Add grouped templates (as carousel items if multiple templates)
                    Object.keys(grouped).forEach(groupId => {
                        const groupTemplates = grouped[groupId];
                        if (groupTemplates.length > 1) {
                            // Create a carousel group
                            result.push({
                                id: `group-${groupId}`,
                                isGroup: true,
                                groupId: parseInt(groupId),
                                templates: groupTemplates,
                                name: groupTemplates[0].template_group?.name || 'Group',
                                description: groupTemplates[0].template_group?.description || '',
                                color: groupTemplates[0].template_group?.color || '#6366f1'
                            });
                        } else {
                            // Single template in group, display as individual
                            result.push(groupTemplates[0]);
                        }
                    });

                    // Add ungrouped templates
                    ungrouped.forEach(template => {
                        result.push(template);
                    });

                    return result;
                },

                getGroupTitle() {
                    if (this.selectedGroup === null) return 'All Templates';
                    if (this.selectedGroup === 'favorites') return 'Favorite Templates';
                    if (this.selectedGroup === 'ungrouped') return 'Ungrouped Templates';

                    // Find group name
                    const groups = @json($templateGroups);
                    const group = groups.find(g => g.id === this.selectedGroup);
                    return group ? group.name : 'Templates';
                },

                loadTemplates() {
                    // Templates are already loaded from the server
                    // This method can be used to refresh templates if needed
                    console.log('Loading templates for group:', this.selectedGroup);
                },

                async toggleFavorite(template) {
                    try {
                        const response = await fetch(`/templates/${template.id}/favorite`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            }
                        });

                        const result = await response.json();

                        if (result.success) {
                            template.is_favorite = result.is_favorite;
                            // Update the template in allTemplates array
                            const index = this.allTemplates.findIndex(t => t.id === template.id);
                            if (index !== -1) {
                                this.allTemplates[index].is_favorite = result.is_favorite;
                            }
                        } else {
                            console.error('Failed to toggle favorite:', result.message);
                        }
                    } catch (error) {
                        console.error('Error toggling favorite:', error);
                    }
                },

                async downloadTemplate(template) {
                    try {
                        // Increment usage count
                        await fetch(`/templates/${template.id}/use`, {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            }
                        });

                        // Download the file
                        window.open(template.file_url, '_blank');

                        // Update usage count in UI
                        template.usage_count++;
                        const index = this.allTemplates.findIndex(t => t.id === template.id);
                        if (index !== -1) {
                            this.allTemplates[index].usage_count++;
                        }
                    } catch (error) {
                        console.error('Error downloading template:', error);
                    }
                },

                editTemplate(template) {
                    // This would open an edit modal
                    console.log('Edit template:', template);
                },

                async deleteTemplate(template) {
                    if (!confirm('Are you sure you want to delete this template? This action cannot be undone.')) {
                        return;
                    }

                    try {
                        const response = await fetch(`/templates/${template.id}`, {
                            method: 'DELETE',
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            }
                        });

                        const result = await response.json();

                        if (result.success) {
                            // Remove template from arrays
                            this.allTemplates = this.allTemplates.filter(t => t.id !== template.id);
                            console.log('Template deleted successfully');
                        } else {
                            console.error('Failed to delete template:', result.message);
                        }
                    } catch (error) {
                        console.error('Error deleting template:', error);
                    }
                },

                handleFileSelect(event) {
                    const file = event.target.files[0];
                    if (file) {
                        this.uploadForm.file = file;
                        if (!this.uploadForm.name) {
                            this.uploadForm.name = file.name.split('.').slice(0, -1).join('.');
                        }
                    }
                },

                handleFileDrop(event) {
                    this.dragOver = false;
                    const files = event.dataTransfer.files;
                    if (files.length > 0) {
                        this.uploadForm.file = files[0];
                        if (!this.uploadForm.name) {
                            this.uploadForm.name = files[0].name.split('.').slice(0, -1).join('.');
                        }
                    }
                },

                async uploadTemplate() {
                    if (!this.uploadForm.file) return;

                    this.uploading = true;
                    this.uploadProgress = 0;

                    const formData = new FormData();
                    formData.append('file', this.uploadForm.file);
                    formData.append('name', this.uploadForm.name);
                    formData.append('description', this.uploadForm.description);
                    if (this.uploadForm.template_group_id) {
                        formData.append('template_group_id', this.uploadForm.template_group_id);
                    }

                    try {
                        const response = await fetch('/templates/upload', {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            }
                        });

                        const result = await response.json();

                        if (result.success) {
                            this.showUploadModal = false;
                            this.resetUploadForm();
                            this.loadTemplates();
                            // Show success message
                            console.log('Template uploaded successfully');
                        } else {
                            console.error('Upload failed:', result.message);
                        }
                    } catch (error) {
                        console.error('Upload error:', error);
                    } finally {
                        this.uploading = false;
                        this.uploadProgress = 0;
                    }
                },

                async createGroup() {
                    try {
                        const response = await fetch('/template-groups', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            },
                            body: JSON.stringify(this.groupForm)
                        });

                        const result = await response.json();

                        if (result.success) {
                            this.showGroupModal = false;
                            this.resetGroupForm();
                            // Reload page to show new group
                            window.location.reload();
                        } else {
                            console.error('Group creation failed:', result.message);
                        }
                    } catch (error) {
                        console.error('Group creation error:', error);
                    }
                },

                resetUploadForm() {
                    this.uploadForm = {
                        file: null,
                        name: '',
                        description: '',
                        template_group_id: ''
                    };
                    document.getElementById('template-file').value = '';
                },

                resetGroupForm() {
                    this.groupForm = {
                        name: '',
                        description: '',
                        color: '#6366f1'
                    };
                },

                handleMultiFileSelect(event) {
                    const files = Array.from(event.target.files);
                    this.addFilesToMultiUpload(files);
                },

                handleMultiFileDrop(event) {
                    this.multiDragOver = false;
                    const files = Array.from(event.dataTransfer.files);
                    this.addFilesToMultiUpload(files);
                },

                addFilesToMultiUpload(files) {
                    // Validate file count (max 20 total)
                    const totalFiles = this.multiUploadForm.files.length + files.length;
                    if (totalFiles > 20) {
                        alert('Maximum 20 files allowed per group');
                        return;
                    }

                    files.forEach(file => {
                        // Validate file type and size
                        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/svg+xml', 'application/pdf'];
                        if (!allowedTypes.includes(file.type) && !file.name.toLowerCase().endsWith('.ai') && !file.name.toLowerCase().endsWith('.psd')) {
                            alert(`File ${file.name} is not a supported format`);
                            return;
                        }

                        if (file.size > 10 * 1024 * 1024) { // 10MB
                            alert(`File ${file.name} is too large (max 10MB)`);
                            return;
                        }

                        // Create file object with preview
                        const fileObj = {
                            file: file,
                            name: file.name,
                            size: file.size,
                            type: file.type,
                            preview: null
                        };

                        // Generate preview for images
                        if (file.type.startsWith('image/')) {
                            const reader = new FileReader();
                            reader.onload = (e) => {
                                fileObj.preview = e.target.result;
                            };
                            reader.readAsDataURL(file);
                        }

                        this.multiUploadForm.files.push(fileObj);
                    });
                },

                removeFile(index) {
                    this.multiUploadForm.files.splice(index, 1);
                },

                async createGroupWithFiles() {
                    if (this.multiUploadForm.files.length === 0 || !this.multiUploadForm.name) return;

                    this.multiUploading = true;
                    this.multiUploadProgress = 0;

                    const formData = new FormData();
                    formData.append('name', this.multiUploadForm.name);
                    formData.append('description', this.multiUploadForm.description);
                    formData.append('color', this.multiUploadForm.color);

                    // Add all files
                    this.multiUploadForm.files.forEach((fileObj, index) => {
                        formData.append(`files[${index}]`, fileObj.file);
                    });

                    try {
                        const response = await fetch('/template-groups/create-with-files', {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            }
                        });

                        const result = await response.json();

                        if (result.success) {
                            this.showMultiUploadModal = false;
                            this.resetMultiUploadForm();
                            // Reload page to show new group and templates
                            window.location.reload();
                        } else {
                            console.error('Group creation failed:', result.message);
                            alert('Failed to create group: ' + result.message);
                        }
                    } catch (error) {
                        console.error('Group creation error:', error);
                        alert('Failed to create group. Please try again.');
                    } finally {
                        this.multiUploading = false;
                        this.multiUploadProgress = 0;
                    }
                },

                resetMultiUploadForm() {
                    this.multiUploadForm = {
                        name: '',
                        description: '',
                        color: '#6366f1',
                        files: []
                    };
                    document.getElementById('multi-template-files').value = '';
                },

                async reorderTemplates(evt) {
                    // Get the new order of template IDs
                    const templateElements = evt.to.children;
                    const templateIds = Array.from(templateElements).map(el => {
                        const templateData = this.filteredTemplates[parseInt(el.dataset.index)];
                        return templateData ? templateData.id : null;
                    }).filter(id => id !== null);

                    try {
                        const response = await fetch('/templates/reorder', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            },
                            body: JSON.stringify({
                                template_ids: templateIds
                            })
                        });

                        const result = await response.json();

                        if (!result.success) {
                            console.error('Failed to reorder templates:', result.message);
                            // Revert the change
                            window.location.reload();
                        }
                    } catch (error) {
                        console.error('Error reordering templates:', error);
                        // Revert the change
                        window.location.reload();
                    }
                }
            }
        }

        // Enhanced Instagram-style Template Carousel Component
        function templateCarousel(templates) {
            return {
                templates: templates,
                currentIndex: 0,

                // Touch/Swipe support
                isDragging: false,
                startX: 0,
                currentX: 0,
                startY: 0,
                currentY: 0,
                threshold: 50, // Minimum distance for swipe
                restraint: 100, // Maximum distance perpendicular to swipe direction
                allowedTime: 300, // Maximum time allowed to travel that distance
                startTime: 0,

                // Auto-play support (optional)
                autoPlay: false,
                autoPlayInterval: null,
                autoPlayDelay: 4000,

                init() {
                    // Initialize auto-play if enabled
                    if (this.autoPlay && this.templates.length > 1) {
                        this.startAutoPlay();
                    }

                    // Add keyboard support
                    this.$el.addEventListener('keydown', (e) => {
                        if (e.key === 'ArrowLeft') {
                            e.preventDefault();
                            this.previousSlide();
                        } else if (e.key === 'ArrowRight') {
                            e.preventDefault();
                            this.nextSlide();
                        }
                    });

                    // Make carousel focusable for keyboard navigation
                    this.$el.setAttribute('tabindex', '0');
                },

                nextSlide() {
                    if (this.templates.length <= 1) return;
                    this.currentIndex = (this.currentIndex + 1) % this.templates.length;
                    this.resetAutoPlay();
                },

                previousSlide() {
                    if (this.templates.length <= 1) return;
                    this.currentIndex = this.currentIndex === 0 ? this.templates.length - 1 : this.currentIndex - 1;
                    this.resetAutoPlay();
                },

                goToSlide(index) {
                    if (this.templates.length <= 1 || index === this.currentIndex) return;
                    this.currentIndex = index;
                    this.resetAutoPlay();
                },

                // Touch event handlers
                handleTouchStart(e) {
                    if (this.templates.length <= 1) return;

                    this.isDragging = true;
                    this.startTime = new Date().getTime();
                    this.startX = e.touches[0].clientX;
                    this.startY = e.touches[0].clientY;
                    this.currentX = this.startX;
                    this.currentY = this.startY;

                    // Add dragging class to disable transitions
                    this.$refs.carouselContainer.classList.add('dragging');
                    this.pauseAutoPlay();
                },

                handleTouchMove(e) {
                    if (!this.isDragging || this.templates.length <= 1) return;

                    e.preventDefault(); // Prevent scrolling
                    this.currentX = e.touches[0].clientX;
                    this.currentY = e.touches[0].clientY;
                },

                handleTouchEnd(e) {
                    if (!this.isDragging || this.templates.length <= 1) return;

                    this.isDragging = false;
                    this.$refs.carouselContainer.classList.remove('dragging');

                    const elapsedTime = new Date().getTime() - this.startTime;
                    const distanceX = this.currentX - this.startX;
                    const distanceY = Math.abs(this.currentY - this.startY);

                    // Check if swipe meets criteria
                    if (elapsedTime <= this.allowedTime && Math.abs(distanceX) >= this.threshold && distanceY <= this.restraint) {
                        if (distanceX < 0) {
                            this.nextSlide(); // Swipe left = next
                        } else {
                            this.previousSlide(); // Swipe right = previous
                        }
                    }

                    this.resetAutoPlay();
                },

                // Mouse event handlers (for desktop drag support)
                handleMouseDown(e) {
                    if (this.templates.length <= 1) return;

                    this.isDragging = true;
                    this.startTime = new Date().getTime();
                    this.startX = e.clientX;
                    this.startY = e.clientY;
                    this.currentX = this.startX;
                    this.currentY = this.startY;

                    this.$refs.carouselContainer.classList.add('dragging');
                    this.pauseAutoPlay();
                    e.preventDefault(); // Prevent text selection
                },

                handleMouseMove(e) {
                    if (!this.isDragging || this.templates.length <= 1) return;

                    this.currentX = e.clientX;
                    this.currentY = e.clientY;
                },

                handleMouseUp(e) {
                    if (!this.isDragging || this.templates.length <= 1) return;

                    this.isDragging = false;
                    this.$refs.carouselContainer.classList.remove('dragging');

                    const elapsedTime = new Date().getTime() - this.startTime;
                    const distanceX = this.currentX - this.startX;
                    const distanceY = Math.abs(this.currentY - this.startY);

                    // Check if drag meets criteria
                    if (elapsedTime <= this.allowedTime && Math.abs(distanceX) >= this.threshold && distanceY <= this.restraint) {
                        if (distanceX < 0) {
                            this.nextSlide(); // Drag left = next
                        } else {
                            this.previousSlide(); // Drag right = previous
                        }
                    }

                    this.resetAutoPlay();
                },

                // Auto-play functionality
                startAutoPlay() {
                    if (this.templates.length <= 1) return;
                    this.autoPlayInterval = setInterval(() => {
                        this.nextSlide();
                    }, this.autoPlayDelay);
                },

                pauseAutoPlay() {
                    if (this.autoPlayInterval) {
                        clearInterval(this.autoPlayInterval);
                        this.autoPlayInterval = null;
                    }
                },

                resetAutoPlay() {
                    if (this.autoPlay) {
                        this.pauseAutoPlay();
                        setTimeout(() => {
                            this.startAutoPlay();
                        }, 1000); // Resume after 1 second
                    }
                },

                getCurrentTemplate() {
                    return this.templates[this.currentIndex];
                },

                downloadCurrentTemplate() {
                    const template = this.getCurrentTemplate();
                    // Use the parent component's download function
                    this.$parent.downloadTemplate(template);
                },

                editCurrentTemplate() {
                    const template = this.getCurrentTemplate();
                    // Use the parent component's edit function
                    this.$parent.editTemplate(template);
                },

                deleteCurrentTemplate() {
                    const template = this.getCurrentTemplate();
                    // Use the parent component's delete function
                    this.$parent.deleteTemplate(template);
                }
            }
        }
    </script>
</x-dashboard-layout>
