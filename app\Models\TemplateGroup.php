<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class TemplateGroup extends Model
{
    protected $fillable = [
        'user_id',
        'name',
        'description',
        'color',
        'sort_order',
        'is_default',
    ];

    protected $casts = [
        'is_default' => 'boolean',
    ];

    /**
     * Get the user that owns the template group.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the templates in this group.
     */
    public function templates(): HasMany
    {
        return $this->hasMany(Template::class)->orderBy('sort_order');
    }

    /**
     * Get the templates count for this group.
     */
    public function getTemplatesCountAttribute(): int
    {
        return $this->templates()->count();
    }

    /**
     * Scope to get groups for a specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to get default groups.
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Scope to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }
}
