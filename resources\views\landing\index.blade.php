<!DOCTYPE html>
<html lang="en" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $seoData['title'] }}</title>
    <meta name="description" content="{{ $seoData['description'] }}">
    <meta name="keywords" content="{{ $seoData['keywords'] }}">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ url()->current() }}">
    <meta property="og:title" content="{{ $seoData['title'] }}">
    <meta property="og:description" content="{{ $seoData['description'] }}">
    <meta property="og:image" content="{{ $seoData['og_image'] }}">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="{{ url()->current() }}">
    <meta property="twitter:title" content="{{ $seoData['title'] }}">
    <meta property="twitter:description" content="{{ $seoData['description'] }}">
    <meta property="twitter:image" content="{{ $seoData['og_image'] }}">

    @vite(['resources/css/app.css', 'resources/js/app.js'])
    <script>
        // Prevent flash of unstyled content
        if (localStorage.getItem('theme') === 'dark' || (!localStorage.getItem('theme') && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            document.documentElement.classList.add('dark');
        }
    </script>
</head>
<body class="app-background">
    <!-- Navigation -->
    <nav class="card-primary border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <!-- Logo -->
                    <div class="flex-shrink-0 flex items-center">
                        <div class="h-8 w-8 bg-indigo-600 rounded-lg flex items-center justify-center">
                            <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <span class="ml-2 text-xl font-bold text-primary">CarouselGen</span>
                    </div>

                    <!-- Navigation Links -->
                    <div class="hidden md:ml-10 md:flex md:space-x-8">
                        <a href="{{ route('home') }}" class="text-primary hover:text-indigo-600 px-3 py-2 text-sm font-medium">Home</a>
                        <a href="{{ route('features') }}" class="text-secondary hover:text-indigo-600 px-3 py-2 text-sm font-medium">Features</a>
                        <a href="{{ route('pricing') }}" class="text-secondary hover:text-indigo-600 px-3 py-2 text-sm font-medium">Pricing</a>
                        <a href="{{ route('about') }}" class="text-secondary hover:text-indigo-600 px-3 py-2 text-sm font-medium">About</a>
                        <a href="{{ route('contact') }}" class="text-secondary hover:text-indigo-600 px-3 py-2 text-sm font-medium">Contact</a>
                    </div>
                </div>

                <div class="flex items-center space-x-4">
                    <!-- Theme Toggle -->
                    <x-theme-toggle />
                    
                    <!-- Auth Links -->
                    @auth
                        <a href="{{ route('dashboard') }}" class="text-secondary hover:text-indigo-600 px-3 py-2 text-sm font-medium">Dashboard</a>
                        <form method="POST" action="{{ route('logout') }}" class="inline">
                            @csrf
                            <button type="submit" class="text-secondary hover:text-indigo-600 px-3 py-2 text-sm font-medium">Logout</button>
                        </form>
                    @else
                        <a href="{{ route('login') }}" class="text-secondary hover:text-indigo-600 px-3 py-2 text-sm font-medium">Login</a>
                        <a href="{{ route('register') }}" class="btn-primary">Get Started</a>
                    @endauth

                    <!-- Mobile menu button -->
                    <div class="md:hidden">
                        <button type="button" class="text-secondary hover:text-primary p-2" x-data x-on:click="$dispatch('toggle-mobile-menu')">
                            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile menu -->
        <div class="md:hidden" x-data="{ open: false }" x-on:toggle-mobile-menu.window="open = !open" x-show="open" x-transition>
            <div class="px-2 pt-2 pb-3 space-y-1 border-t border-gray-200 dark:border-gray-700">
                <a href="{{ route('home') }}" class="block text-primary hover:text-indigo-600 px-3 py-2 text-base font-medium">Home</a>
                <a href="{{ route('features') }}" class="block text-secondary hover:text-indigo-600 px-3 py-2 text-base font-medium">Features</a>
                <a href="{{ route('pricing') }}" class="block text-secondary hover:text-indigo-600 px-3 py-2 text-base font-medium">Pricing</a>
                <a href="{{ route('about') }}" class="block text-secondary hover:text-indigo-600 px-3 py-2 text-base font-medium">About</a>
                <a href="{{ route('contact') }}" class="block text-secondary hover:text-indigo-600 px-3 py-2 text-base font-medium">Contact</a>
                @auth
                    <a href="{{ route('dashboard') }}" class="block text-secondary hover:text-indigo-600 px-3 py-2 text-base font-medium">Dashboard</a>
                @else
                    <a href="{{ route('login') }}" class="block text-secondary hover:text-indigo-600 px-3 py-2 text-base font-medium">Login</a>
                    <a href="{{ route('register') }}" class="block btn-primary mt-2">Get Started</a>
                @endauth
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="relative overflow-hidden">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32">
            <div class="lg:grid lg:grid-cols-12 lg:gap-8">
                <div class="sm:text-center md:max-w-2xl md:mx-auto lg:col-span-6 lg:text-left">
                    <h1 class="text-4xl font-bold text-primary sm:text-5xl lg:text-6xl">
                        Create Stunning 
                        <span class="text-indigo-600">Carousels</span> 
                        with AI
                    </h1>
                    <p class="mt-3 text-base text-secondary sm:mt-5 sm:text-xl lg:text-lg xl:text-xl">
                        Transform your content into beautiful, engaging carousels with our AI-powered carousel generator. Perfect for social media, presentations, and marketing.
                    </p>
                    <div class="mt-8 sm:max-w-lg sm:mx-auto sm:text-center lg:text-left lg:mx-0">
                        <div class="flex flex-col sm:flex-row gap-4">
                            <a href="{{ route('register') }}" class="btn-primary flex-1 text-center py-3 px-6">
                                Start Creating Free
                            </a>
                            <a href="#demo" class="btn-secondary flex-1 text-center py-3 px-6">
                                Watch Demo
                            </a>
                        </div>
                        <p class="mt-3 text-sm text-tertiary">
                            No credit card required • {{ $stats['carousels_created'] }} carousels created
                        </p>
                    </div>
                </div>
                <div class="mt-12 relative sm:max-w-lg sm:mx-auto lg:mt-0 lg:max-w-none lg:mx-0 lg:col-span-6 lg:flex lg:items-center">
                    <div class="relative mx-auto w-full rounded-lg shadow-lg lg:max-w-md">
                        <div class="card-elevated p-8 text-center">
                            <div class="w-16 h-16 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-semibold text-primary mb-2">AI-Powered Creation</h3>
                            <p class="text-secondary">Generate professional carousels in seconds with our advanced AI technology.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="py-12 border-t border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-2 gap-4 md:grid-cols-4">
                <div class="text-center">
                    <div class="text-2xl font-bold text-indigo-600">{{ $stats['carousels_created'] }}</div>
                    <div class="text-sm text-secondary">Carousels Created</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-indigo-600">{{ $stats['active_users'] }}</div>
                    <div class="text-sm text-secondary">Active Users</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-indigo-600">{{ $stats['templates'] }}</div>
                    <div class="text-sm text-secondary">Templates</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-indigo-600">{{ $stats['satisfaction'] }}</div>
                    <div class="text-sm text-secondary">Satisfaction</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-24">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h2 class="text-3xl font-bold text-primary sm:text-4xl">
                    Everything you need to create amazing carousels
                </h2>
                <p class="mt-4 text-xl text-secondary">
                    Powerful features designed to make carousel creation effortless and professional.
                </p>
            </div>

            <div class="mt-20 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
                @foreach($features as $feature)
                <div class="card-primary p-6 text-center">
                    <div class="w-12 h-12 bg-indigo-100 dark:bg-indigo-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                        @if($feature['icon'] === 'brain')
                            <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                        @elseif($feature['icon'] === 'template')
                            <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z"></path>
                            </svg>
                        @elseif($feature['icon'] === 'share')
                            <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                            </svg>
                        @else
                            <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                        @endif
                    </div>
                    <h3 class="text-lg font-semibold text-primary mb-2">{{ $feature['title'] }}</h3>
                    <p class="text-secondary">{{ $feature['description'] }}</p>
                </div>
                @endforeach
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="py-24 bg-gray-50 dark:bg-gray-800/50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h2 class="text-3xl font-bold text-primary sm:text-4xl">
                    Loved by creators worldwide
                </h2>
                <p class="mt-4 text-xl text-secondary">
                    See what our users are saying about CarouselGen.
                </p>
            </div>

            <div class="mt-20 grid grid-cols-1 gap-8 lg:grid-cols-3">
                @foreach($testimonials as $testimonial)
                <div class="card-primary p-6">
                    <div class="flex items-center mb-4">
                        <img class="h-10 w-10 rounded-full" src="{{ $testimonial['avatar'] }}" alt="{{ $testimonial['name'] }}">
                        <div class="ml-4">
                            <div class="text-sm font-medium text-primary">{{ $testimonial['name'] }}</div>
                            <div class="text-sm text-secondary">{{ $testimonial['role'] }} at {{ $testimonial['company'] }}</div>
                        </div>
                    </div>
                    <blockquote class="text-secondary">
                        "{{ $testimonial['content'] }}"
                    </blockquote>
                </div>
                @endforeach
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section class="py-24" id="pricing">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h2 class="text-3xl font-bold text-primary sm:text-4xl">
                    Simple, transparent pricing
                </h2>
                <p class="mt-4 text-xl text-secondary">
                    Choose the plan that's right for you. Upgrade or downgrade at any time.
                </p>
            </div>

            <div class="mt-20 grid grid-cols-1 gap-8 lg:grid-cols-3">
                @foreach($pricing as $plan)
                <div class="card-primary p-8 {{ $plan['popular'] ? 'ring-2 ring-indigo-600' : '' }} relative">
                    @if($plan['popular'])
                        <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                            <span class="bg-indigo-600 text-white px-4 py-1 text-sm font-medium rounded-full">Most Popular</span>
                        </div>
                    @endif

                    <div class="text-center">
                        <h3 class="text-lg font-semibold text-primary">{{ $plan['name'] }}</h3>
                        <div class="mt-4">
                            <span class="text-4xl font-bold text-primary">${{ $plan['price'] }}</span>
                            <span class="text-secondary">/ {{ $plan['period'] }}</span>
                        </div>
                        <p class="mt-4 text-secondary">{{ $plan['description'] }}</p>
                    </div>

                    <ul class="mt-8 space-y-4">
                        @foreach($plan['features'] as $feature)
                        <li class="flex items-start">
                            <svg class="h-5 w-5 text-green-500 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span class="text-secondary">{{ $feature }}</span>
                        </li>
                        @endforeach
                    </ul>

                    <div class="mt-8">
                        <a href="{{ $plan['name'] === 'Free' ? route('register') : '#' }}"
                           class="btn-primary w-full text-center py-3 px-6 {{ $plan['popular'] ? '' : 'btn-secondary' }}">
                            {{ $plan['cta'] }}
                        </a>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </section>

    <!-- Newsletter Section -->
    <section class="py-16 bg-indigo-50 dark:bg-indigo-900/20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h2 class="text-3xl font-bold text-primary">Stay Updated</h2>
                <p class="mt-4 text-xl text-secondary">Get the latest updates, tips, and exclusive content delivered to your inbox.</p>
            </div>
            
            <form action="{{ route('newsletter.subscribe') }}" method="POST" class="mt-8 max-w-md mx-auto" x-data="newsletterForm()">
                @csrf
                <div class="flex gap-4">
                    <input type="email" name="email" placeholder="Enter your email" required
                           class="input-field flex-1" x-model="email">
                    <button type="submit" class="btn-primary" :disabled="loading" x-text="loading ? 'Subscribing...' : 'Subscribe'">
                        Subscribe
                    </button>
                </div>
                
                @if(session('newsletter_success'))
                    <div class="mt-4 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                        <p class="text-green-800 dark:text-green-200 text-sm">{{ session('newsletter_success') }}</p>
                    </div>
                @endif
                
                @error('newsletter_general')
                    <div class="mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                        <p class="text-red-800 dark:text-red-200 text-sm">{{ $message }}</p>
                    </div>
                @enderror
            </form>
        </div>
    </section>

    <!-- Footer -->
    <footer class="card-primary border-t border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center">
                        <div class="h-8 w-8 bg-indigo-600 rounded-lg flex items-center justify-center">
                            <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <span class="ml-2 text-xl font-bold text-primary">CarouselGen</span>
                    </div>
                    <p class="mt-4 text-secondary">
                        Create stunning carousels with AI-powered technology. Transform your content into engaging visual stories.
                    </p>
                </div>
                
                <div>
                    <h3 class="text-sm font-semibold text-primary uppercase tracking-wider">Product</h3>
                    <ul class="mt-4 space-y-4">
                        <li><a href="{{ route('features') }}" class="text-secondary hover:text-indigo-600">Features</a></li>
                        <li><a href="{{ route('pricing') }}" class="text-secondary hover:text-indigo-600">Pricing</a></li>
                        <li><a href="#" class="text-secondary hover:text-indigo-600">Templates</a></li>
                        <li><a href="#" class="text-secondary hover:text-indigo-600">API</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-sm font-semibold text-primary uppercase tracking-wider">Support</h3>
                    <ul class="mt-4 space-y-4">
                        <li><a href="{{ route('contact') }}" class="text-secondary hover:text-indigo-600">Contact</a></li>
                        <li><a href="#" class="text-secondary hover:text-indigo-600">Help Center</a></li>
                        <li><a href="#" class="text-secondary hover:text-indigo-600">Privacy Policy</a></li>
                        <li><a href="#" class="text-secondary hover:text-indigo-600">Terms of Service</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="mt-8 pt-8 border-t border-gray-200 dark:border-gray-700">
                <p class="text-center text-secondary">
                    © {{ date('Y') }} CarouselGen. All rights reserved.
                </p>
            </div>
        </div>
    </footer>

    <script>
        function newsletterForm() {
            return {
                email: '',
                loading: false,
                
                async submit() {
                    this.loading = true;
                    // Form will submit normally, this is just for UI feedback
                }
            }
        }
    </script>
</body>
</html>
