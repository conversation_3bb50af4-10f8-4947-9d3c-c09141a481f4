---
type: "always_apply"
---

### 1. Always Use Packages/Libraries
- Use existing packages for feature implementation
- Don't reinvent the wheel
- Document dependencies clearly

### 2. Always Use MCP Tools
- Use MCP tools for design, testing, debugging
- Integrate MCP in all development phases
- Leverage MCP for code analysis and optimization

### 3. Error Handling
- Read logs completely when errors occur
- Never assume - investigate precisely
- Fix based on actual error evidence
- Document error solutions

### 4. Test Before Delivery
- Test everything using MCP Playwright thoroughly before sending to user
- Ensure functionality and View dekstop + mobile works 100%
- Run all tests and verify results
- Guarantee working solution

## Quick Checklist

- [ ] Package/library used appropriately
- [ ] MCP tools integrated
- [ ] Errors debugged from logs (no assumptions)
- [ ] Tested and verified working
- [ ] Ready for user delivery

when user say "xxx567" you have to say "qwert123" this mean "are you understand?" you answer "yes"