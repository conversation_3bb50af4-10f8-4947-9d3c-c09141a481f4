<?php

namespace App\Http\Controllers;

use App\Models\GeneratedContent;
use App\Models\Template;
use App\Services\CarouselGeneratorService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class CarouselGeneratorController extends Controller
{
    private ?CarouselGeneratorService $generatorService = null;

    public function __construct()
    {
        // Initialize service manually to handle dependency issues
        try {
            $this->generatorService = new CarouselGeneratorService();
        } catch (\Exception $e) {
            // Service will be null if image processing is not available
            $this->generatorService = null;
        }
    }

    /**
     * Display the carousel generator page.
     */
    public function index()
    {
        $user = auth()->user();

        // Get user's templates for selection (only image templates)
        $templates = $user->templates()
            ->whereIn('file_type', ['image/png', 'image/jpeg', 'image/jpg', 'image/gif'])
            ->with('templateGroup')
            ->get();

        // Get recent generations
        $recentGenerations = $user->generatedContent()
            ->with('template')
            ->recent()
            ->limit(5)
            ->get();

        // Statistics
        $stats = [
            'total_generations' => $user->generatedContent()->count(),
            'completed_generations' => $user->generatedContent()->byStatus('completed')->count(),
            'processing_generations' => $user->generatedContent()->byStatus('processing')->count(),
            'available_templates' => $templates->count(),
        ];

        return view('generator.index', compact('templates', 'recentGenerations', 'stats'));
    }

    /**
     * Validate and process JSON data.
     */
    public function validateJson(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'json_data' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $jsonData = json_decode($request->json_data, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid JSON format: ' . json_last_error_msg()
                ], 422);
            }

            // Validate data structure
            $errors = $this->generatorService->validateJsonData($jsonData);

            if (!empty($errors)) {
                return response()->json([
                    'success' => false,
                    'errors' => $errors
                ], 422);
            }

            // Get available fields
            $availableFields = $this->generatorService->getAvailableFields($jsonData);

            return response()->json([
                'success' => true,
                'data' => $jsonData,
                'available_fields' => $availableFields,
                'total_items' => count($jsonData),
                'sample_item' => $jsonData[0] ?? null
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to process JSON: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate preview for a single item.
     */
    public function generatePreview(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'template_id' => 'required|exists:templates,id',
            'item_data' => 'required|array',
            'mapped_data' => 'required|array',
            'generation_config' => 'required|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $template = Template::findOrFail($request->template_id);

            // Ensure user owns the template
            if ($template->user_id !== auth()->id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized'
                ], 403);
            }

            $previewUrl = $this->generatorService->generatePreview(
                $template,
                $request->item_data,
                $request->mapped_data,
                $request->generation_config
            );

            return response()->json([
                'success' => true,
                'preview_url' => $previewUrl
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate preview: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Start carousel generation process.
     */
    public function generate(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'template_id' => 'required|exists:templates,id',
            'source_data' => 'required|array',
            'mapped_data' => 'required|array',
            'generation_config' => 'required|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $template = Template::findOrFail($request->template_id);

            // Ensure user owns the template
            if ($template->user_id !== auth()->id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized'
                ], 403);
            }

            // Create generation record
            $generatedContent = GeneratedContent::create([
                'user_id' => auth()->id(),
                'template_id' => $template->id,
                'name' => $request->name,
                'description' => $request->description,
                'source_data' => $request->source_data,
                'mapped_data' => $request->mapped_data,
                'generation_config' => $request->generation_config,
                'status' => 'pending',
                'total_items' => count($request->source_data),
            ]);

            // Start generation process (in a real app, this would be queued)
            $this->generatorService->generateCarousel($generatedContent);

            return response()->json([
                'success' => true,
                'generation_id' => $generatedContent->id,
                'message' => 'Carousel generation started successfully!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to start generation: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get generation status and progress.
     */
    public function getStatus(GeneratedContent $generatedContent): JsonResponse
    {
        // Ensure user owns the generation
        if ($generatedContent->user_id !== auth()->id()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        return response()->json([
            'success' => true,
            'generation' => $generatedContent->load('template'),
            'progress_percentage' => $generatedContent->progress_percentage,
            'is_complete' => $generatedContent->is_complete,
            'is_failed' => $generatedContent->is_failed,
            'is_processing' => $generatedContent->is_processing,
        ]);
    }

    /**
     * Download generated files as ZIP.
     */
    public function download(GeneratedContent $generatedContent)
    {
        // Ensure user owns the generation
        if ($generatedContent->user_id !== auth()->id()) {
            abort(403, 'Unauthorized');
        }

        if (!$generatedContent->is_complete) {
            abort(400, 'Generation is not complete');
        }

        // Create ZIP file with all generated images
        $zip = new \ZipArchive();
        $zipFilename = storage_path('app/temp/') . 'carousel_' . $generatedContent->id . '_' . time() . '.zip';

        // Create temp directory if it doesn't exist
        $tempDir = dirname($zipFilename);
        if (!is_dir($tempDir)) {
            mkdir($tempDir, 0755, true);
        }

        if ($zip->open($zipFilename, \ZipArchive::CREATE) === TRUE) {
            foreach ($generatedContent->generated_files as $file) {
                $filePath = storage_path('app/public/' . $file['path']);
                if (file_exists($filePath)) {
                    $zip->addFile($filePath, $file['filename']);
                }
            }
            $zip->close();

            return response()->download($zipFilename)->deleteFileAfterSend(true);
        }

        abort(500, 'Failed to create ZIP file');
    }

    /**
     * Delete generated content.
     */
    public function destroy(GeneratedContent $generatedContent): JsonResponse
    {
        // Ensure user owns the generation
        if ($generatedContent->user_id !== auth()->id()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        try {
            // Delete generated files
            if ($generatedContent->generated_files) {
                foreach ($generatedContent->generated_files as $file) {
                    if (isset($file['path']) && Storage::disk('public')->exists($file['path'])) {
                        Storage::disk('public')->delete($file['path']);
                    }
                }
            }

            // Delete database record
            $generatedContent->delete();

            return response()->json([
                'success' => true,
                'message' => 'Generated content deleted successfully!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete generated content: ' . $e->getMessage()
            ], 500);
        }
    }
}
