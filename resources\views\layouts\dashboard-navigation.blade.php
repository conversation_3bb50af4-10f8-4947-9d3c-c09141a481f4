<nav class="flex flex-1 flex-col">
    <ul role="list" class="flex flex-1 flex-col gap-y-7">
        <li>
            <ul role="list" class="-mx-2 space-y-1">
                <li>
                    <a href="{{ route('dashboard') }}" 
                       class="group flex gap-x-3 rounded-md p-2 text-sm font-semibold {{ request()->routeIs('dashboard') ? 'bg-gray-50 dark:bg-gray-800 text-indigo-600' : 'text-secondary hover:text-indigo-600 hover:bg-gray-50 dark:hover:bg-gray-800' }}">
                        <svg class="h-6 w-6 shrink-0 {{ request()->routeIs('dashboard') ? 'text-indigo-600' : 'text-gray-400 group-hover:text-indigo-600' }}" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" />
                        </svg>
                        Dashboard
                    </a>
                </li>
                <li>
                    <a href="#" 
                       class="group flex gap-x-3 rounded-md p-2 text-sm font-semibold text-secondary hover:text-indigo-600 hover:bg-gray-50 dark:hover:bg-gray-800">
                        <svg class="h-6 w-6 shrink-0 text-gray-400 group-hover:text-indigo-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        My Carousels
                    </a>
                </li>
                <li>
                    <a href="{{ route('templates.index') }}"
                       class="group flex gap-x-3 rounded-md p-2 text-sm font-semibold {{ request()->routeIs('templates.*') ? 'bg-gray-50 dark:bg-gray-800 text-indigo-600' : 'text-secondary hover:text-indigo-600 hover:bg-gray-50 dark:hover:bg-gray-800' }}">
                        <svg class="h-6 w-6 shrink-0 {{ request()->routeIs('templates.*') ? 'text-indigo-600' : 'text-gray-400 group-hover:text-indigo-600' }}" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z"></path>
                        </svg>
                        Templates
                    </a>
                </li>
                <li>
                    <a href="{{ route('generator.index') }}"
                       class="group flex gap-x-3 rounded-md p-2 text-sm font-semibold {{ request()->routeIs('generator.*') ? 'bg-gray-50 dark:bg-gray-800 text-indigo-600' : 'text-secondary hover:text-indigo-600 hover:bg-gray-50 dark:hover:bg-gray-800' }}">
                        <svg class="h-6 w-6 shrink-0 {{ request()->routeIs('generator.*') ? 'text-indigo-600' : 'text-gray-400 group-hover:text-indigo-600' }}" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        Generator
                    </a>
                </li>
                <li>
                    <a href="#" 
                       class="group flex gap-x-3 rounded-md p-2 text-sm font-semibold text-secondary hover:text-indigo-600 hover:bg-gray-50 dark:hover:bg-gray-800">
                        <svg class="h-6 w-6 shrink-0 text-gray-400 group-hover:text-indigo-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 6a7.5 7.5 0 1 0 7.5 7.5h-7.5V6Z" />
                            <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 10.5H21A7.5 7.5 0 0 0 13.5 3v7.5Z" />
                        </svg>
                        Analytics
                    </a>
                </li>
                <li>
                    <a href="#" 
                       class="group flex gap-x-3 rounded-md p-2 text-sm font-semibold text-secondary hover:text-indigo-600 hover:bg-gray-50 dark:hover:bg-gray-800">
                        <svg class="h-6 w-6 shrink-0 text-gray-400 group-hover:text-indigo-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 0 1-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 0 1 1.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 0 0-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 0 1-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 0 0-3.375-3.375h-1.5a1.125 1.125 0 0 1-1.125-1.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H9.75" />
                        </svg>
                        Exports
                    </a>
                </li>
            </ul>
        </li>
        
        <!-- Quick Actions Section -->
        <li>
            <div class="text-xs font-semibold text-secondary uppercase tracking-wider">Quick Actions</div>
            <ul role="list" class="-mx-2 mt-2 space-y-1">
                <li>
                    <a href="#" 
                       class="group flex gap-x-3 rounded-md p-2 text-sm font-semibold text-secondary hover:text-indigo-600 hover:bg-gray-50 dark:hover:bg-gray-800">
                        <span class="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 text-[0.625rem] font-medium text-gray-400 group-hover:border-indigo-600 group-hover:text-indigo-600">+</span>
                        <span class="truncate">New Carousel</span>
                    </a>
                </li>
                <li>
                    <a href="#" 
                       class="group flex gap-x-3 rounded-md p-2 text-sm font-semibold text-secondary hover:text-indigo-600 hover:bg-gray-50 dark:hover:bg-gray-800">
                        <span class="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 text-[0.625rem] font-medium text-gray-400 group-hover:border-indigo-600 group-hover:text-indigo-600">T</span>
                        <span class="truncate">Browse Templates</span>
                    </a>
                </li>
                <li>
                    <a href="#" 
                       class="group flex gap-x-3 rounded-md p-2 text-sm font-semibold text-secondary hover:text-indigo-600 hover:bg-gray-50 dark:hover:bg-gray-800">
                        <span class="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 text-[0.625rem] font-medium text-gray-400 group-hover:border-indigo-600 group-hover:text-indigo-600">A</span>
                        <span class="truncate">AI Assistant</span>
                    </a>
                </li>
            </ul>
        </li>
        
        <!-- User Profile -->
        <li class="-mx-6 mt-auto">
            <div class="hidden lg:block">
                <div class="relative" x-data="{ open: false }">
                    <button type="button" class="flex items-center gap-x-4 px-6 py-3 text-sm font-semibold text-primary hover:bg-gray-50 dark:hover:bg-gray-800 w-full" @click="open = !open">
                        <img class="h-8 w-8 rounded-full bg-gray-50" src="https://ui-avatars.com/api/?name={{ urlencode(auth()->user()->name) }}&color=7c3aed&background=ede9fe" alt="{{ auth()->user()->name }}">
                        <span class="sr-only">Your profile</span>
                        <span aria-hidden="true">{{ auth()->user()->name }}</span>
                        <svg class="ml-auto h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                        </svg>
                    </button>
                    
                    <div x-show="open" @click.away="open = false" x-transition
                         class="absolute bottom-full left-0 z-10 mb-2 w-full origin-bottom card-primary shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                        <div class="py-1">
                            <a href="{{ route('profile.edit') }}" class="block px-4 py-2 text-sm text-secondary hover:bg-gray-100 dark:hover:bg-gray-700">Profile</a>
                            <a href="#" class="block px-4 py-2 text-sm text-secondary hover:bg-gray-100 dark:hover:bg-gray-700">Settings</a>
                            <form method="POST" action="{{ route('logout') }}">
                                @csrf
                                <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-secondary hover:bg-gray-100 dark:hover:bg-gray-700">
                                    Log Out
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </li>
    </ul>
</nav>
