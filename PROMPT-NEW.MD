## PROMPT 5 OF 12: JSON UPLOAD & MAPPING

### CONTEXT:
This is prompt 5 of 12 in the development sequence. At this stage, we are implementing the JSON upload and key mapping system which allows users to upload JSON data and map keys to template elements for carousel generation.

### WHAT TO DO:
**Implementation Order (MANDATORY):**
1. **Backend**: Create JSON parser service, validation, and mapping storage
2. **Frontend**: Build JSON upload interface and key mapping UI using DaisyUI
3. **Integration**: Connect JSON parsing with drag-drop key mapping using Alpine.js
4. **Testing**: Validate JSON parsing and key mapping functionality

### FEATURE: JSON Upload and Key Mapping System
### TECH: Laravel JSON validation + DaisyUI file input, table, drag-drop + Alpine.js
### UI: JSON file upload, key preview table, drag-drop mapping interface using DaisyUI
### THEME: Forest theme with DaisyUI file input, table, and drag-drop components
### LOGIC: JSON parsing, validation, key extraction, mapping storage
### DATA: JSON data storage, key mapping configuration
### ERROR HANDLING: JSON validation errors, mapping conflicts with clear feedback

### OUTPUT REQUIREMENTS:
- JSON file upload with format validation
- JSON key preview with data structure display
- Drag-and-drop interface for mapping JSON keys to template elements
- Mapping validation and conflict resolution
- Preview of mapped data before generation

### SUCCESS CRITERIA:
- [ ] JSON files upload and parse correctly
- [ ] JSON keys display in organized table format
- [ ] Drag-drop mapping interface works smoothly
- [ ] Mapping validation prevents conflicts
- [ ] Forest theme applied to all components
- [ ] Alpine.js JSON parsing and mapping working

---

## PROMPT 6 OF 12: CAROUSEL GENERATOR

### CONTEXT:
This is prompt 6 of 12 in the development sequence. At this stage, we are implementing the main carousel generation engine which combines templates, JSON data, and mappings to create Instagram carousels with text customization.

### WHAT TO DO:
**Implementation Order (MANDATORY):**
1. **Backend**: Create CarouselGeneratorService, image processing, text overlay system
2. **Frontend**: Build generation interface with preview and customization using DaisyUI
3. **Integration**: Connect generation process with real-time preview using Alpine.js
4. **Testing**: Validate carousel generation and text customization works

### FEATURE: Carousel Generation Engine
### TECH: Intervention Image + DaisyUI preview, slider, color picker + Alpine.js
### UI: Generation interface with live preview, text customization controls using DaisyUI
### THEME: Forest theme with DaisyUI form controls and preview components
### LOGIC: Image processing, text overlay, font sizing, color application
### DATA: Carousel table with generated files and configuration
### ERROR HANDLING: Generation failures, image processing errors with user feedback

### OUTPUT REQUIREMENTS:
- Real-time carousel preview with text overlays
- Text customization controls (size, color, position)
- Batch generation for multiple JSON entries
- Generated carousel download functionality
- Progress indicators for generation process

### SUCCESS CRITERIA:
- [ ] Carousels generate with proper text overlays
- [ ] Text customization controls work in real-time
- [ ] Batch generation processes multiple entries
- [ ] Generated files can be downloaded
- [ ] Forest theme applied to all controls
- [ ] Alpine.js generation interactions working

---

## PROMPT 7 OF 12: CONTENT LIBRARY

### CONTEXT:
This is prompt 7 of 12 in the development sequence. At this stage, we are implementing the content library system which manages all generated carousels and provides organization, filtering, and management capabilities.

### WHAT TO DO:
**Implementation Order (MANDATORY):**
1. **Backend**: Create content library controller, filtering, and management operations
2. **Frontend**: Build library interface with grid view and filters using DaisyUI
3. **Integration**: Connect filtering and bulk operations with Alpine.js
4. **Testing**: Validate content management and filtering functionality

### FEATURE: Generated Content Library Management
### TECH: Laravel pagination + DaisyUI grid, filter, modal components + Alpine.js
### UI: Content grid with filters, bulk selection, preview modals using DaisyUI
### THEME: Forest theme with DaisyUI grid, filter, and modal components
### LOGIC: Content filtering, bulk operations, preview generation
### DATA: Enhanced Carousel model with metadata and status
### ERROR HANDLING: Content loading errors, bulk operation failures

### OUTPUT REQUIREMENTS:
- Responsive grid layout for generated content
- Filtering by date, template, status
- Bulk selection and operations (delete, download)
- Content preview with carousel navigation
- Search functionality for content discovery

### SUCCESS CRITERIA:
- [ ] Content displays in responsive grid layout
- [ ] Filtering works for all specified criteria
- [ ] Bulk operations process selected items
- [ ] Content preview shows carousel slides
- [ ] Forest theme applied consistently
- [ ] Alpine.js filtering and selection working

---

## PROMPT 8 OF 12: SOCIAL MEDIA INTEGRATION

### CONTEXT:
This is prompt 8 of 12 in the development sequence. At this stage, we are implementing social media account connections which allow users to connect their Instagram and Facebook accounts for automated posting.

### WHAT TO DO:
**Implementation Order (MANDATORY):**
1. **Backend**: Create SocialAccount model, OAuth integration, API connections
2. **Frontend**: Build account connection interface using DaisyUI components
3. **Integration**: Connect OAuth flow with account management using Alpine.js
4. **Testing**: Validate social media account connections work

### FEATURE: Social Media Account Integration
### TECH: Laravel Socialite + DaisyUI card, button, badge components + Alpine.js
### UI: Account connection cards, OAuth flow, connection status using DaisyUI
### THEME: Forest theme with DaisyUI card and button components
### LOGIC: OAuth authentication, token storage, account validation
### DATA: SocialAccount table with platform credentials
### ERROR HANDLING: OAuth failures, API connection errors with clear feedback

### OUTPUT REQUIREMENTS:
- Social media platform connection cards
- OAuth authentication flow for Instagram and Facebook
- Connection status indicators and management
- Account disconnection with confirmation
- Platform-specific settings and permissions

### SUCCESS CRITERIA:
- [ ] Instagram Business accounts can be connected
- [ ] Facebook Pages can be connected
- [ ] OAuth flow works without errors
- [ ] Connection status displays accurately
- [ ] Forest theme applied to all components
- [ ] Alpine.js OAuth interactions working

---

## PROMPT 9 OF 12: SCHEDULER SYSTEM

### CONTEXT:
This is prompt 9 of 12 in the development sequence. At this stage, we are implementing the post scheduling system which allows users to schedule carousel posts with date ranges, auto-spacing, and caption mapping.

### WHAT TO DO:
**Implementation Order (MANDATORY):**
1. **Backend**: Create ScheduledPost model, scheduling logic, queue system
2. **Frontend**: Build scheduling interface with calendar and settings using DaisyUI
3. **Integration**: Connect scheduling controls with calendar view using Alpine.js
4. **Testing**: Validate scheduling functionality and queue processing

### FEATURE: Post Scheduling System
### TECH: Laravel Queues + DaisyUI calendar, form, timeline components + Alpine.js
### UI: Scheduling interface with calendar view, date range picker using DaisyUI
### THEME: Forest theme with DaisyUI calendar and form components
### LOGIC: Schedule calculation, auto-spacing, queue job processing
### DATA: ScheduledPost table with timing and status tracking
### ERROR HANDLING: Scheduling conflicts, queue failures with user notifications

### OUTPUT REQUIREMENTS:
- Calendar view for scheduled posts
- Date range scheduling with auto-spacing
- Content source selection (upload or library)
- Caption mapping from JSON data
- Schedule management and editing

### SUCCESS CRITERIA:
- [ ] Posts can be scheduled with date ranges
- [ ] Auto-spacing calculates optimal timing
- [ ] Calendar view shows scheduled posts
- [ ] Content sources work properly
- [ ] Forest theme applied to scheduling interface
- [ ] Alpine.js calendar interactions working

---

## PROMPT 10 OF 12: ANALYTICS DASHBOARD

### CONTEXT:
This is prompt 10 of 12 in the development sequence. At this stage, we are implementing the analytics dashboard which provides users with insights into their template usage, generation statistics, and posting performance.

### WHAT TO DO:
**Implementation Order (MANDATORY):**
1. **Backend**: Create analytics service, data aggregation, metrics calculation
2. **Frontend**: Build analytics dashboard with charts and metrics using DaisyUI
3. **Integration**: Connect real-time data updates with Alpine.js
4. **Testing**: Validate analytics data accuracy and dashboard functionality

### FEATURE: Analytics and Performance Dashboard
### TECH: Laravel analytics + DaisyUI stats, chart, table components + Alpine.js
### UI: Analytics dashboard with charts, metrics, and data tables using DaisyUI
### THEME: Forest theme with DaisyUI stats and chart components
### LOGIC: Data aggregation, metrics calculation, performance tracking
### DATA: Analytics table with user metrics and timestamps
### ERROR HANDLING: Data loading errors, calculation failures with fallbacks

### OUTPUT REQUIREMENTS:
- Usage statistics with visual charts
- Template performance metrics
- Generation and posting analytics
- Exportable analytics reports
- Real-time data updates

### SUCCESS CRITERIA:
- [ ] Analytics display accurate user data
- [ ] Charts render properly with forest theme
- [ ] Metrics calculate correctly
- [ ] Data exports work as expected
- [ ] Forest theme applied to all analytics components
- [ ] Alpine.js data updates working

---

## PROMPT 11 OF 12: BILLING SYSTEM

### CONTEXT:
This is prompt 11 of 12 in the development sequence. At this stage, we are implementing the subscription billing system which manages user plans, usage limits, and payment processing for the SaaS platform.

### WHAT TO DO:
**Implementation Order (MANDATORY):**
1. **Backend**: Create subscription models, Stripe integration, usage tracking
2. **Frontend**: Build billing interface with plan management using DaisyUI
3. **Integration**: Connect payment flow with subscription management using Alpine.js
4. **Testing**: Validate billing functionality and subscription management

### FEATURE: Subscription Billing System
### TECH: Laravel Cashier + DaisyUI pricing, modal, alert components + Alpine.js
### UI: Billing dashboard with plan comparison, payment forms using DaisyUI
### THEME: Forest theme with DaisyUI pricing and form components
### LOGIC: Subscription management, usage tracking, payment processing
### DATA: Subscription tables with plan details and usage limits
### ERROR HANDLING: Payment failures, subscription errors with user guidance

### OUTPUT REQUIREMENTS:
- Subscription plan overview and management
- Usage tracking with limit indicators
- Payment method management
- Invoice history and downloads
- Plan upgrade/downgrade functionality

### SUCCESS CRITERIA:
- [ ] Subscription plans display correctly
- [ ] Payment processing works without errors
- [ ] Usage limits enforce properly
- [ ] Invoice generation works
- [ ] Forest theme applied to billing interface
- [ ] Alpine.js payment interactions working

---

## PROMPT 12 OF 12: ADMIN DASHBOARD

### CONTEXT:
This is prompt 12 of 12 in the development sequence. At this stage, we are implementing the admin dashboard which provides system administrators with user management, analytics, and system monitoring capabilities.

### WHAT TO DO:
**Implementation Order (MANDATORY):**
1. **Backend**: Create admin controllers, user management, system analytics
2. **Frontend**: Build admin interface with user management using DaisyUI
3. **Integration**: Connect admin controls with data management using Alpine.js
4. **Testing**: Validate admin functionality and access controls

### FEATURE: Admin Dashboard and User Management
### TECH: Laravel admin + DaisyUI admin components, tables, modals + Alpine.js
### UI: Admin dashboard with user management, system metrics using DaisyUI
### THEME: Forest theme with DaisyUI admin-focused components
### LOGIC: User management, system monitoring, admin analytics
### DATA: Admin views of all user data and system metrics
### ERROR HANDLING: Admin operation errors with detailed logging

### OUTPUT REQUIREMENTS:
- Comprehensive admin dashboard with system overview
- User management with account controls
- System analytics and monitoring
- Content moderation capabilities
- Admin-specific navigation and permissions

### SUCCESS CRITERIA:
- [ ] Admin dashboard loads with system metrics
- [ ] User management operations work properly
- [ ] System analytics display accurately
- [ ] Admin permissions enforce correctly
- [ ] Forest theme applied to admin interface
- [ ] Alpine.js admin interactions working

### TESTING PHASE (MANDATORY):
- Test functionality on mobile (320px) and desktop (1024px+) viewports
- Verify all DaisyUI components are properly implemented using MCP server documentation
- Test error handling and validation scenarios
- Confirm Alpine.js interactivity works properly
- Validate MySQL database operations

### ADDITIONAL RULES:
- **PRIORITY 1**: Use DaisyUI MCP server for component discovery and documentation
- **PRIORITY 2**: Use existing libraries/packages when they meet the requirements
- **PRIORITY 3**: Only code from scratch when no suitable library exists
- Must use forest theme with DaisyUI theme system
- Must use ONLY DaisyUI components (NO CUSTOM COMPONENTS)
- Must follow Laravel conventions
