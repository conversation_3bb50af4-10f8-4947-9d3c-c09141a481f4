<?php

namespace App\Http\Controllers;

use App\Models\TemplateGroup;
use App\Models\Template;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

class TemplateGroupController extends Controller
{
    /**
     * Create a new template group.
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = auth()->user();

            // Get the next sort order
            $maxSortOrder = $user->templateGroups()->max('sort_order') ?? 0;

            $templateGroup = TemplateGroup::create([
                'user_id' => $user->id,
                'name' => $request->name,
                'description' => $request->description,
                'color' => $request->color ?? '#6366f1',
                'sort_order' => $maxSortOrder + 1,
            ]);

            return response()->json([
                'success' => true,
                'template_group' => $templateGroup,
                'message' => 'Template group created successfully!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create template group: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a template group with multiple files.
     */
    public function createWithFiles(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'files' => 'required|array|min:1|max:20',
            'files.*' => 'required|file|mimes:jpeg,jpg,png,gif,svg,pdf,ai,psd|max:10240', // 10MB max per file
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = auth()->user();

            // Start database transaction
            DB::beginTransaction();

            // Create the template group
            $maxSortOrder = $user->templateGroups()->max('sort_order') ?? 0;
            $templateGroup = TemplateGroup::create([
                'user_id' => $user->id,
                'name' => $request->name,
                'description' => $request->description,
                'color' => $request->color ?? '#6366f1',
                'sort_order' => $maxSortOrder + 1,
            ]);

            // Process each file
            $templates = [];
            $files = $request->file('files');

            foreach ($files as $index => $file) {
                // Generate unique filename
                $filename = Str::uuid() . '.' . $file->getClientOriginalExtension();
                $path = "templates/{$user->id}/" . $filename;

                // Store the file
                $storedPath = $file->storeAs("templates/{$user->id}", $filename, 'public');

                // Generate thumbnail for images
                $thumbnailPath = null;
                if (str_starts_with($file->getMimeType(), 'image/')) {
                    $thumbnailPath = $this->generateThumbnail($storedPath, $user->id);
                }

                // Get file metadata
                $metadata = $this->getFileMetadata($file, $storedPath);

                // Create template record
                $template = Template::create([
                    'user_id' => $user->id,
                    'template_group_id' => $templateGroup->id,
                    'name' => pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME),
                    'description' => null,
                    'file_path' => $storedPath,
                    'file_name' => $file->getClientOriginalName(),
                    'file_type' => $file->getMimeType(),
                    'file_size' => $file->getSize(),
                    'thumbnail_path' => $thumbnailPath,
                    'metadata' => $metadata,
                    'sort_order' => $index + 1,
                ]);

                $templates[] = $template->append(['file_url', 'thumbnail_url', 'formatted_file_size', 'is_image']);
            }

            // Commit transaction
            DB::commit();

            return response()->json([
                'success' => true,
                'template_group' => $templateGroup,
                'templates' => $templates,
                'message' => 'Template group created successfully with ' . count($templates) . ' templates!'
            ]);

        } catch (\Exception $e) {
            // Rollback transaction
            DB::rollback();

            return response()->json([
                'success' => false,
                'message' => 'Failed to create template group: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update a template group.
     */
    public function update(Request $request, TemplateGroup $templateGroup): JsonResponse
    {
        // Ensure user owns the template group
        if ($templateGroup->user_id !== auth()->id()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $templateGroup->update($request->only(['name', 'description', 'color']));

            return response()->json([
                'success' => true,
                'template_group' => $templateGroup,
                'message' => 'Template group updated successfully!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update template group: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a template group.
     */
    public function destroy(TemplateGroup $templateGroup): JsonResponse
    {
        // Ensure user owns the template group
        if ($templateGroup->user_id !== auth()->id()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        // Check if group has templates
        if ($templateGroup->templates()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete group that contains templates. Please move or delete templates first.'
            ], 422);
        }

        try {
            $templateGroup->delete();

            return response()->json([
                'success' => true,
                'message' => 'Template group deleted successfully!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete template group: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reorder template groups.
     */
    public function reorder(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'groups' => 'required|array',
            'groups.*.id' => 'required|exists:template_groups,id',
            'groups.*.sort_order' => 'required|integer|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = auth()->user();

            foreach ($request->groups as $groupData) {
                $templateGroup = TemplateGroup::find($groupData['id']);

                // Ensure user owns the template group
                if ($templateGroup && $templateGroup->user_id === $user->id) {
                    $templateGroup->update(['sort_order' => $groupData['sort_order']]);
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Template groups reordered successfully!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to reorder template groups: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate thumbnail for image files.
     */
    private function generateThumbnail(string $filePath, int $userId): ?string
    {
        try {
            $manager = new ImageManager(new Driver());
            $fullPath = storage_path('app/public/' . $filePath);

            if (!file_exists($fullPath)) {
                return null;
            }

            $image = $manager->read($fullPath);
            $image->scale(width: 300); // Resize to 300px width, maintain aspect ratio

            $thumbnailFilename = 'thumb_' . basename($filePath);
            $thumbnailPath = "templates/{$userId}/thumbnails/" . $thumbnailFilename;
            $thumbnailFullPath = storage_path('app/public/' . $thumbnailPath);

            // Create thumbnails directory if it doesn't exist
            $thumbnailDir = dirname($thumbnailFullPath);
            if (!is_dir($thumbnailDir)) {
                mkdir($thumbnailDir, 0755, true);
            }

            $image->save($thumbnailFullPath);

            return $thumbnailPath;
        } catch (\Exception $e) {
            // If thumbnail generation fails, return null
            return null;
        }
    }

    /**
     * Get file metadata.
     */
    private function getFileMetadata($file, string $storedPath): array
    {
        $metadata = [
            'original_name' => $file->getClientOriginalName(),
            'extension' => $file->getClientOriginalExtension(),
        ];

        // Add image-specific metadata
        if (str_starts_with($file->getMimeType(), 'image/')) {
            try {
                $fullPath = storage_path('app/public/' . $storedPath);
                if (file_exists($fullPath)) {
                    $imageInfo = getimagesize($fullPath);
                    if ($imageInfo) {
                        $metadata['width'] = $imageInfo[0];
                        $metadata['height'] = $imageInfo[1];
                        $metadata['aspect_ratio'] = round($imageInfo[0] / $imageInfo[1], 2);
                    }
                }
            } catch (\Exception $e) {
                // If we can't get image info, continue without it
            }
        }

        return $metadata;
    }
}
