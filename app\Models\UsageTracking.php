<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UsageTracking extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'action',
        'resource_type',
        'resource_id',
        'metadata',
        'tracked_at',
    ];

    protected $casts = [
        'metadata' => 'array',
        'tracked_at' => 'datetime',
    ];

    /**
     * Get the user that owns the usage tracking record
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Track a user action
     */
    public static function track(int $userId, string $action, ?string $resourceType = null, ?int $resourceId = null, ?array $metadata = null): self
    {
        return self::create([
            'user_id' => $userId,
            'action' => $action,
            'resource_type' => $resourceType,
            'resource_id' => $resourceId,
            'metadata' => $metadata,
            'tracked_at' => now(),
        ]);
    }

    /**
     * Get usage statistics for a user
     */
    public static function getUserStats(int $userId, ?string $period = null): array
    {
        $query = self::where('user_id', $userId);

        if ($period) {
            $query->where('tracked_at', '>=', now()->sub($period));
        }

        $stats = $query->selectRaw('action, COUNT(*) as count')
            ->groupBy('action')
            ->pluck('count', 'action')
            ->toArray();

        return $stats;
    }
}
