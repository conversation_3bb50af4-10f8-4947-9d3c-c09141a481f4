# CarouselGen - Blueprint

## TECH STACK SPECIFICATIONS
- **Backend**: Lara<PERSON> 11+ (Full-stack with Blade)
- **Frontend**: Blade Templates + Tailwind Plus templates + Alpine.js
- **Database**: MySQL (MANDATORY)
- **Database Name**: carouselgen_db (Must match application name)
- **Authentication**: <PERSON><PERSON> (MANDATORY)
- **Asset Management**: Vite
- **UI Framework**: Tailwind CSS + Tailwind Plus templates (MANDATORY)
- **JavaScript**: Alpine.js for interactivity
- **Testing**: PHPUnit (Backend) + Pest (Feature tests)

## PROJECT ARCHITECTURE TEMPLATES
```
app/
├── Http/
│   ├── Controllers/
│   │   ├── Auth/
│   │   ├── Admin/
│   │   ├── User/
│   │   ├── TemplateController.php
│   │   ├── CarouselController.php
│   │   ├── SchedulerController.php
│   │   └── AnalyticsController.php
│   ├── Middleware/
│   │   ├── AdminMiddleware.php
│   │   └── SubscriptionMiddleware.php
│   └── Requests/
├── Models/
│   ├── User.php
│   ├── Template.php
│   ├── TemplateGroup.php
│   ├── GeneratedContent.php
│   ├── SocialAccount.php
│   ├── ScheduledPost.php
│   ├── Subscription.php
│   └── Analytics.php
├── Services/
│   ├── CarouselGeneratorService.php
│   ├── SocialMediaService.php
│   ├── SchedulerService.php
│   └── BillingService.php
└── Jobs/
    ├── GenerateCarouselJob.php
    ├── PostToSocialMediaJob.php
    └── ProcessScheduledPostsJob.php

resources/
├── views/
│   ├── layouts/
│   │   ├── app.blade.php
│   │   ├── guest.blade.php
│   │   └── admin.blade.php
│   ├── auth/
│   ├── dashboard/
│   ├── templates/
│   ├── generator/
│   ├── library/
│   ├── scheduler/
│   ├── analytics/
│   ├── billing/
│   ├── admin/
│   └── landing/
├── css/
│   └── app.css (includes Tailwind Plus global styles)
└── js/
    └── app.js

public/
├── template/ (MANDATORY - Tailwind Plus templates)
│   ├── auth/
│   ├── landing/
│   ├── user-dashboard/
│   ├── admin-dashboard/
│   └── components/
├── uploads/
│   ├── templates/
│   └── generated/
└── assets/
```

## DATABASE BLUEPRINT
- **Database**: MySQL (MANDATORY)
- **Database Name**: carouselgen_db (not default Laravel names)

### Core Tables:
```sql
-- Users and Authentication
users: id, name, email, email_verified_at, password, role, subscription_plan, created_at, updated_at
password_reset_tokens: email, token, created_at

-- Template Management
template_groups: id, user_id, name, description, created_at, updated_at
templates: id, template_group_id, name, file_path, position, text_areas, created_at, updated_at

-- Content Generation
generated_content: id, user_id, template_group_id, json_data, file_paths, status, created_at, updated_at

-- Social Media Integration
social_accounts: id, user_id, platform, account_id, access_token, refresh_token, expires_at, created_at, updated_at
scheduled_posts: id, user_id, content_id, social_account_id, caption, scheduled_at, posted_at, status, created_at, updated_at

-- Billing and Subscriptions
subscriptions: id, user_id, plan_name, status, current_period_start, current_period_end, created_at, updated_at
usage_tracking: id, user_id, feature, count, period_start, period_end, created_at, updated_at

-- Analytics
analytics: id, user_id, metric_type, metric_value, date, created_at, updated_at
```

### Relationships:
- User hasMany TemplateGroups, GeneratedContent, SocialAccounts, ScheduledPosts
- TemplateGroup belongsTo User, hasMany Templates
- Template belongsTo TemplateGroup
- GeneratedContent belongsTo User, TemplateGroup
- SocialAccount belongsTo User
- ScheduledPost belongsTo User, GeneratedContent, SocialAccount

### Indexing:
- Primary keys: All tables
- Foreign keys: user_id, template_group_id, social_account_id, content_id
- Search fields: email, name, status, platform, scheduled_at
- Performance indexes: created_at, updated_at, status fields

## WEB DESIGN STANDARDS
- Route format: `/resource` or `/resource/{id}`
- Response: Blade views with data
- Auth flow: Laravel Breeze session-based
- HTTP methods: GET, POST, PUT, DELETE with proper CSRF
- API endpoints: `/api/v1/` for AJAX requests
- File uploads: Laravel Storage with validation
- Error handling: Try-catch with user-friendly messages

## UI/UX STANDARDS
- Template library: Tailwind Plus templates (MANDATORY)
- Copy Templates: Use template files from `/public/template` folder with copy-paste approach
- Layout patterns: Header + Sidebar + Main content using Tailwind Plus templates
- Styling framework: Tailwind CSS + Tailwind Plus theme system
- Template variants: Modern, classic, minimal, corporate, creative styles
- Responsive: Mobile (320px+) and Desktop (1024px+) viewports
- Interactivity: Alpine.js for dynamic behavior

## GLOBAL STYLES
### Tailwind Plus Template Integration
Tailwind Plus provides pre-built template collections with consistent styling and components. Use templates exclusively from the `/public/template` folder.

### Installation and Setup
- **Tailwind CSS**: Base framework (required)
- **Tailwind Plus**: Download/create Tailwind Plus theme CSS
- **CSS Configuration**: Include Tailwind Plus global styles in `resources/css/app.css`
- **Template Selection**: Choose from template variants (modern, classic, minimal, corporate, creative)

### Tailwind Plus Template Usage (MANDATORY)
- **Use Tailwind Plus templates exclusively** for all UI elements
- **Reference `/public/template` folder** for template discovery and implementation
- **Copy templates approach**: Copy exact template files and classes from local template directory
- **No custom templates**: Use appropriate Tailwind Plus templates for required functionality

### Tailwind Plus Template System
- **Template Variants**: Use Tailwind Plus template variants (modern, classic, minimal, corporate, creative)
- **Template Implementation**: Apply Tailwind Plus CSS classes and structure from template files
- **Template Discovery**: Browse `/public/template` folder to explore available templates
- **Template Switching**: Implement template variant switching with Alpine.js
- **Consistent Styling**: Use Tailwind Plus global CSS for consistent theming across templates

### Copy Templates Methodology
- **Template Discovery**: Browse `/public/template` folder to find appropriate templates for functionality
- **File Access**: Reference local template files for exact HTML structure and CSS classes
- **Template Copying**: Copy exact template files and classes from `/public/template` directory
- **Blade Integration**: Integrate template structure and classes into Blade templates
- **No Custom CSS**: Use Tailwind Plus templates exclusively, avoid custom styling

### Template Implementation Strategy
- **Authentication Pages**: Copy from `/public/template/auth/` (login, register, forgot-password)
- **Landing Pages**: Copy from `/public/template/landing/` (hero, features, pricing, contact)
- **User Dashboards**: Copy from `/public/template/user-dashboard/` (profile, settings, data views)
- **Admin Dashboards**: Copy from `/public/template/admin-dashboard/` (analytics, management, reports)
- **Components**: Copy from `/public/template/components/` (buttons, forms, modals, tables)
- **CSS Setup**: Include Tailwind Plus global CSS in `resources/css/app.css`

## INTEGRATION POINTS
- Third-party services: Stripe/PayPal for billing, SendGrid for emails
- External APIs: Instagram Graph API, Facebook Graph API, Twitter API v2
- File storage: Laravel Storage with S3/DigitalOcean Spaces
- Queue system: Redis for job processing
- Caching: Redis for session and application caching
- Image processing: Intervention Image for carousel generation

## LOGGING SYSTEM
- Log format: Laravel default with context
- Log levels: emergency, alert, critical, error, warning, notice, info, debug
- Log location: storage/logs/laravel.log
- Error handling: Try-catch blocks with proper logging
- User activity: Track template uploads, generations, posts scheduled
- System monitoring: API calls, job failures, performance metrics
