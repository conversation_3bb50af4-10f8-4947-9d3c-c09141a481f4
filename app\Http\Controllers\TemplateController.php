<?php

namespace App\Http\Controllers;

use App\Models\Template;
use App\Models\TemplateGroup;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

class TemplateController extends Controller
{
    /**
     * Display the template management page.
     */
    public function index()
    {
        $user = auth()->user();

        // Get user's template groups with template counts and templates with computed attributes
        $templateGroups = $user->templateGroups()
            ->withCount('templates')
            ->with(['templates' => function ($query) {
                $query->ordered();
            }])
            ->get()
            ->map(function ($group) {
                $group->templates = $group->templates->map(function ($template) {
                    return $template->append(['file_url', 'thumbnail_url', 'formatted_file_size', 'is_image']);
                });
                return $group;
            });

        // Get ungrouped templates with computed attributes
        $ungroupedTemplates = $user->templates()
            ->ungrouped()
            ->with('templateGroup')
            ->get()
            ->map(function ($template) {
                return $template->append(['file_url', 'thumbnail_url', 'formatted_file_size', 'is_image']);
            });

        // Get recent templates
        $recentTemplates = $user->templates()
            ->recentlyUsed()
            ->limit(6)
            ->get()
            ->map(function ($template) {
                return $template->append(['file_url', 'thumbnail_url', 'formatted_file_size', 'is_image']);
            });

        // Get favorite templates
        $favoriteTemplates = $user->templates()
            ->favorites()
            ->limit(6)
            ->get()
            ->map(function ($template) {
                return $template->append(['file_url', 'thumbnail_url', 'formatted_file_size', 'is_image']);
            });

        // Statistics
        $stats = [
            'total_templates' => $user->templates()->count(),
            'total_groups' => $templateGroups->count(),
            'favorite_templates' => $user->templates()->favorites()->count(),
            'storage_used' => $this->getStorageUsed($user->id),
        ];

        return view('templates.index', compact(
            'templateGroups',
            'ungroupedTemplates',
            'recentTemplates',
            'favoriteTemplates',
            'stats'
        ));
    }

    /**
     * Upload a new template.
     */
    public function upload(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|file|mimes:jpeg,jpg,png,gif,svg,pdf,ai,psd|max:10240', // 10MB max
            'name' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:1000',
            'template_group_id' => 'nullable|exists:template_groups,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $file = $request->file('file');
            $user = auth()->user();

            // Generate unique filename
            $filename = Str::uuid() . '.' . $file->getClientOriginalExtension();
            $path = "templates/{$user->id}/" . $filename;

            // Store the file
            $storedPath = $file->storeAs("templates/{$user->id}", $filename, 'public');

            // Generate thumbnail for images
            $thumbnailPath = null;
            if (str_starts_with($file->getMimeType(), 'image/')) {
                $thumbnailPath = $this->generateThumbnail($storedPath, $user->id);
            }

            // Get file metadata
            $metadata = $this->getFileMetadata($file, $storedPath);

            // Create template record
            $template = Template::create([
                'user_id' => $user->id,
                'template_group_id' => $request->template_group_id,
                'name' => $request->name ?: pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME),
                'description' => $request->description,
                'file_path' => $storedPath,
                'file_name' => $file->getClientOriginalName(),
                'file_type' => $file->getMimeType(),
                'file_size' => $file->getSize(),
                'thumbnail_path' => $thumbnailPath,
                'metadata' => $metadata,
            ]);

            return response()->json([
                'success' => true,
                'template' => $template->load('templateGroup'),
                'message' => 'Template uploaded successfully!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload template: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a template.
     */
    public function destroy(Template $template): JsonResponse
    {
        // Ensure user owns the template
        if ($template->user_id !== auth()->id()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        try {
            // Delete files from storage
            if (Storage::disk('public')->exists($template->file_path)) {
                Storage::disk('public')->delete($template->file_path);
            }

            if ($template->thumbnail_path && Storage::disk('public')->exists($template->thumbnail_path)) {
                Storage::disk('public')->delete($template->thumbnail_path);
            }

            // Delete database record
            $template->delete();

            return response()->json([
                'success' => true,
                'message' => 'Template deleted successfully!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete template: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle template favorite status.
     */
    public function toggleFavorite(Template $template): JsonResponse
    {
        // Ensure user owns the template
        if ($template->user_id !== auth()->id()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $template->update(['is_favorite' => !$template->is_favorite]);

        return response()->json([
            'success' => true,
            'is_favorite' => $template->is_favorite,
            'message' => $template->is_favorite ? 'Added to favorites!' : 'Removed from favorites!'
        ]);
    }

    /**
     * Increment template usage count.
     */
    public function incrementUsage(Template $template): JsonResponse
    {
        // Ensure user owns the template
        if ($template->user_id !== auth()->id()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $template->incrementUsage();

        return response()->json([
            'success' => true,
            'usage_count' => $template->usage_count,
            'message' => 'Usage tracked successfully!'
        ]);
    }

    /**
     * Update template details.
     */
    public function update(Request $request, Template $template): JsonResponse
    {
        // Ensure user owns the template
        if ($template->user_id !== auth()->id()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'template_group_id' => 'nullable|exists:template_groups,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $template->update($request->only(['name', 'description', 'template_group_id']));

        return response()->json([
            'success' => true,
            'template' => $template->load('templateGroup'),
            'message' => 'Template updated successfully!'
        ]);
    }

    /**
     * Get templates for a specific group.
     */
    public function getGroupTemplates(TemplateGroup $templateGroup): JsonResponse
    {
        // Ensure user owns the template group
        if ($templateGroup->user_id !== auth()->id()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $templates = $templateGroup->templates()->ordered()->get()->map(function ($template) {
            return $template->append(['file_url', 'thumbnail_url', 'formatted_file_size', 'is_image']);
        });

        return response()->json([
            'success' => true,
            'templates' => $templates
        ]);
    }

    /**
     * Reorder templates within a group or globally.
     */
    public function reorderTemplates(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'template_ids' => 'required|array',
            'template_ids.*' => 'required|integer|exists:templates,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $templateIds = $request->template_ids;
            $user = auth()->user();

            // Update sort order for each template
            foreach ($templateIds as $index => $templateId) {
                $template = Template::where('id', $templateId)
                    ->where('user_id', $user->id)
                    ->first();

                if ($template) {
                    $template->update(['sort_order' => $index + 1]);
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Templates reordered successfully!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to reorder templates: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate thumbnail for image files.
     */
    private function generateThumbnail(string $filePath, int $userId): ?string
    {
        try {
            $manager = new ImageManager(new Driver());
            $fullPath = storage_path('app/public/' . $filePath);

            if (!file_exists($fullPath)) {
                return null;
            }

            $image = $manager->read($fullPath);
            $image->scale(width: 300); // Resize to 300px width, maintain aspect ratio

            $thumbnailFilename = 'thumb_' . basename($filePath);
            $thumbnailPath = "templates/{$userId}/thumbnails/" . $thumbnailFilename;
            $thumbnailFullPath = storage_path('app/public/' . $thumbnailPath);

            // Create thumbnails directory if it doesn't exist
            $thumbnailDir = dirname($thumbnailFullPath);
            if (!is_dir($thumbnailDir)) {
                mkdir($thumbnailDir, 0755, true);
            }

            $image->save($thumbnailFullPath);

            return $thumbnailPath;
        } catch (\Exception $e) {
            // If thumbnail generation fails, return null
            return null;
        }
    }

    /**
     * Get file metadata.
     */
    private function getFileMetadata($file, string $storedPath): array
    {
        $metadata = [
            'original_name' => $file->getClientOriginalName(),
            'extension' => $file->getClientOriginalExtension(),
        ];

        // Add image-specific metadata
        if (str_starts_with($file->getMimeType(), 'image/')) {
            try {
                $fullPath = storage_path('app/public/' . $storedPath);
                if (file_exists($fullPath)) {
                    $imageInfo = getimagesize($fullPath);
                    if ($imageInfo) {
                        $metadata['width'] = $imageInfo[0];
                        $metadata['height'] = $imageInfo[1];
                        $metadata['aspect_ratio'] = round($imageInfo[0] / $imageInfo[1], 2);
                    }
                }
            } catch (\Exception $e) {
                // If we can't get image info, continue without it
            }
        }

        return $metadata;
    }

    /**
     * Calculate storage used by user.
     */
    private function getStorageUsed(int $userId): string
    {
        $totalBytes = Template::where('user_id', $userId)->sum('file_size');

        $units = ['B', 'KB', 'MB', 'GB'];
        for ($i = 0; $totalBytes > 1024 && $i < count($units) - 1; $i++) {
            $totalBytes /= 1024;
        }

        return round($totalBytes, 2) . ' ' . $units[$i];
    }
}
