<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\View\View;

class LandingController extends Controller
{
    /**
     * Display the landing page
     */
    public function index(): View
    {
        $seoData = [
            'title' => 'CarouselGen - Create Stunning Carousels with AI',
            'description' => 'Transform your content into beautiful, engaging carousels with our AI-powered carousel generator. Perfect for social media, presentations, and marketing.',
            'keywords' => 'carousel generator, AI carousel, social media content, presentation maker, marketing tools',
            'og_image' => asset('images/og-image.jpg'),
        ];

        $stats = [
            'carousels_created' => '10,000+',
            'active_users' => '5,000+',
            'templates' => '100+',
            'satisfaction' => '99%',
        ];

        $features = [
            [
                'title' => 'AI-Powered Generation',
                'description' => 'Let our AI create stunning carousels from your content automatically.',
                'icon' => 'brain',
            ],
            [
                'title' => 'Professional Templates',
                'description' => 'Choose from hundreds of professionally designed templates.',
                'icon' => 'template',
            ],
            [
                'title' => 'Multi-Platform Export',
                'description' => 'Export your carousels for Instagram, LinkedIn, Twitter, and more.',
                'icon' => 'share',
            ],
            [
                'title' => 'Real-time Collaboration',
                'description' => 'Work together with your team in real-time.',
                'icon' => 'users',
            ],
        ];

        $testimonials = [
            [
                'name' => 'Sarah Johnson',
                'role' => 'Marketing Manager',
                'company' => 'TechCorp',
                'content' => 'CarouselGen has revolutionized our social media strategy. We create engaging content in minutes, not hours.',
                'avatar' => 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
            ],
            [
                'name' => 'Michael Chen',
                'role' => 'Content Creator',
                'company' => 'Creative Studio',
                'content' => 'The AI suggestions are incredibly smart. It understands my brand and creates carousels that perfectly match my style.',
                'avatar' => 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
            ],
            [
                'name' => 'Emily Rodriguez',
                'role' => 'Social Media Specialist',
                'company' => 'Growth Agency',
                'content' => 'Our engagement rates have increased by 300% since we started using CarouselGen. It\'s a game-changer.',
                'avatar' => 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
            ],
        ];

        $pricing = [
            [
                'name' => 'Free',
                'price' => 0,
                'period' => 'forever',
                'description' => 'Perfect for getting started',
                'features' => [
                    '5 carousels per month',
                    'Basic templates',
                    'Standard export quality',
                    'Community support',
                ],
                'cta' => 'Get Started',
                'popular' => false,
            ],
            [
                'name' => 'Pro',
                'price' => 19,
                'period' => 'month',
                'description' => 'For content creators and small teams',
                'features' => [
                    'Unlimited carousels',
                    'Premium templates',
                    'HD export quality',
                    'AI-powered suggestions',
                    'Priority support',
                    'Brand customization',
                ],
                'cta' => 'Start Free Trial',
                'popular' => true,
            ],
            [
                'name' => 'Team',
                'price' => 49,
                'period' => 'month',
                'description' => 'For agencies and larger teams',
                'features' => [
                    'Everything in Pro',
                    'Team collaboration',
                    'Advanced analytics',
                    'White-label options',
                    'Custom integrations',
                    'Dedicated support',
                ],
                'cta' => 'Contact Sales',
                'popular' => false,
            ],
        ];

        return view('landing.index', compact('seoData', 'stats', 'features', 'testimonials', 'pricing'));
    }

    /**
     * Display the features page
     */
    public function features(): View
    {
        return view('landing.features');
    }

    /**
     * Display the pricing page
     */
    public function pricing(): View
    {
        return view('landing.pricing');
    }

    /**
     * Display the about page
     */
    public function about(): View
    {
        return view('landing.about');
    }
}
